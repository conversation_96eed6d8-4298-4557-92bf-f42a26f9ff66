🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_11_09_59_55
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_11_09_59_59
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 2.0468, Loss: 23.7495, Revenue: 0.4493, LoadBalance: -1.0827, Tasks: [S0:798(43.8%), S1:251(13.8%), S2:775(42.5%)], ActorGrad: 105.3367, CriticGrad: 77.9041, Advantage: μ=0.277, σ=1.774, range=[-2.76, 5.41]

📊 Epoch 1 训练统计:
  平均奖励: 8.8136
  平均损失: 65.4536
  平均收益率: 0.3245
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 8.745, revenue_rate: 0.2616, efficiency: 0.0811, distance: 3.8428, memory: 0.0433, power: 0.1223
Test Summary - Avg reward: 10.147±5.879, revenue_rate: 0.3101±0.0439, efficiency: 0.1118, completion_rate: 0.3597, distance: 5.3890, memory: 0.0410, power: 0.1493
Load Balance - Avg balance score: -0.5194±0.2633
Task Distribution by Satellite:
  Satellite 1: 1301 tasks (37.04%)
  Satellite 2: 1095 tasks (31.18%)
  Satellite 3: 1116 tasks (31.78%)
✅ 验证完成 - Epoch 1, reward: 10.147, revenue_rate: 0.3101, distance: 5.3890, memory: 0.0410, power: 0.1493
  ✅ 训练验证差距正常: -1.3333
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_11_09_59_59 (验证集奖励: 10.1469)

开始训练 Epoch 2/3
Batch 0: Reward: 11.4798, Loss: 81.3293, Revenue: 0.3646, LoadBalance: -0.5207, Tasks: [S0:464(34.5%), S1:461(34.3%), S2:419(31.2%)], ActorGrad: 39.4431, CriticGrad: 114.1262, Advantage: μ=1.431, σ=1.055, range=[-0.87, 3.26]

📊 Epoch 2 训练统计:
  平均奖励: 10.4524
  平均损失: 73.3892
  平均收益率: 0.3110
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 7.262, revenue_rate: 0.2571, efficiency: 0.0823, distance: 4.2065, memory: 0.0536, power: 0.1270
Test Summary - Avg reward: 8.473±5.723, revenue_rate: 0.2825±0.0511, efficiency: 0.0950, completion_rate: 0.3316, distance: 4.9505, memory: 0.0474, power: 0.1373
Load Balance - Avg balance score: -0.5526±0.2981
Task Distribution by Satellite:
  Satellite 1: 1105 tasks (34.23%)
  Satellite 2: 966 tasks (29.93%)
  Satellite 3: 1157 tasks (35.84%)
✅ 验证完成 - Epoch 2, reward: 8.473, revenue_rate: 0.2825, distance: 4.9505, memory: 0.0474, power: 0.1373
  ✅ 训练验证差距正常: 1.9791

开始训练 Epoch 3/3
Batch 0: Reward: 11.8737, Loss: 90.1377, Revenue: 0.2975, LoadBalance: -0.4449, Tasks: [S0:358(32.9%), S1:343(31.5%), S2:387(35.6%)], ActorGrad: 41.6567, CriticGrad: 98.0655, Advantage: μ=1.421, σ=1.068, range=[-1.88, 2.97]

📊 Epoch 3 训练统计:
  平均奖励: 10.1814
  平均损失: 70.1144
  平均收益率: 0.3140
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 3.541, revenue_rate: 0.3017, efficiency: 0.0965, distance: 4.5689, memory: -0.0149, power: 0.1250
Test Summary - Avg reward: 8.407±6.590, revenue_rate: 0.3108±0.0413, efficiency: 0.1132, completion_rate: 0.3634, distance: 5.3545, memory: 0.0540, power: 0.1460
Load Balance - Avg balance score: -0.6026±0.3107
Task Distribution by Satellite:
  Satellite 1: 1063 tasks (29.96%)
  Satellite 2: 1313 tasks (37.01%)
  Satellite 3: 1172 tasks (33.03%)
✅ 验证完成 - Epoch 3, reward: 8.407, revenue_rate: 0.3108, distance: 5.3545, memory: 0.0540, power: 0.1460
  ✅ 训练验证差距正常: 1.7743
训练完成

开始测试模型...
Test Batch 4/4, reward: 5.396, revenue_rate: 0.2750, efficiency: 0.0900, distance: 5.2013, memory: 0.1348, power: 0.1298
Test Summary - Avg reward: 8.876±5.886, revenue_rate: 0.3062±0.0403, efficiency: 0.1107, completion_rate: 0.3603, distance: 5.3705, memory: 0.0540, power: 0.1459
Load Balance - Avg balance score: -0.5635±0.2851
Task Distribution by Satellite:
  Satellite 1: 1030 tasks (29.26%)
  Satellite 2: 1301 tasks (36.96%)
  Satellite 3: 1189 tasks (33.78%)
测试完成 - 平均奖励: 8.876, 平均星座收益率: 0.3062
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_11_09_59_59
   平均奖励: 8.8764
   收益率: 0.3062

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_11_10_08_12
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 6.0097, Loss: 33.1709, Revenue: 0.4493, LoadBalance: -1.0827, Tasks: [S0:798(43.8%), S1:251(13.8%), S2:775(42.5%)], ActorGrad: 69.0954, CriticGrad: 285.8851, Advantage: μ=1.451, σ=1.026, range=[-0.32, 3.61]

📊 Epoch 1 训练统计:
  平均奖励: 6.8399
  平均损失: 21.5573
  平均收益率: 0.3319
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 6.342, revenue_rate: 0.3207, efficiency: 0.1179, distance: 5.1052, memory: -0.0049, power: 0.1473
Test Summary - Avg reward: 7.064±3.101, revenue_rate: 0.3074±0.0468, efficiency: 0.1113, completion_rate: 0.3591, distance: 5.1924, memory: 0.0343, power: 0.1472
Load Balance - Avg balance score: -0.5804±0.2969
Task Distribution by Satellite:
  Satellite 1: 1112 tasks (31.74%)
  Satellite 2: 1363 tasks (38.90%)
  Satellite 3: 1029 tasks (29.37%)
✅ 验证完成 - Epoch 1, reward: 7.064, revenue_rate: 0.3074, distance: 5.1924, memory: 0.0343, power: 0.1472
  ✅ 训练验证差距正常: -0.2242
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_11_10_08_12 (验证集奖励: 7.0641)

开始训练 Epoch 2/3
Batch 0: Reward: 6.6852, Loss: 13.2626, Revenue: 0.2988, LoadBalance: -0.5452, Tasks: [S0:419(37.4%), S1:361(32.2%), S2:340(30.4%)], ActorGrad: 53.6670, CriticGrad: 45.1542, Advantage: μ=1.288, σ=1.230, range=[-0.89, 3.68]

📊 Epoch 2 训练统计:
  平均奖励: 7.0214
  平均损失: 17.1138
  平均收益率: 0.3094
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 6.235, revenue_rate: 0.2897, efficiency: 0.0985, distance: 4.7693, memory: -0.0093, power: 0.1406
Test Summary - Avg reward: 5.168±2.640, revenue_rate: 0.3455±0.0462, efficiency: 0.1421, completion_rate: 0.4089, distance: 6.0840, memory: 0.0415, power: 0.1637
Load Balance - Avg balance score: -1.0140±0.3538
Task Distribution by Satellite:
  Satellite 1: 981 tasks (24.50%)
  Satellite 2: 2071 tasks (51.72%)
  Satellite 3: 952 tasks (23.78%)
✅ 验证完成 - Epoch 2, reward: 5.168, revenue_rate: 0.3455, distance: 6.0840, memory: 0.0415, power: 0.1637
  ✅ 训练验证差距正常: 1.8535

开始训练 Epoch 3/3
Batch 0: Reward: 7.2586, Loss: 18.6624, Revenue: 0.3062, LoadBalance: -0.6116, Tasks: [S0:313(29.6%), S1:444(42.0%), S2:299(28.3%)], ActorGrad: 57.1115, CriticGrad: 46.3519, Advantage: μ=1.252, σ=1.268, range=[-1.10, 3.29]

📊 Epoch 3 训练统计:
  平均奖励: 7.1090
  平均损失: 16.4714
  平均收益率: 0.3118
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 2.090, revenue_rate: 0.2164, efficiency: 0.0584, distance: 4.5558, memory: 0.1149, power: 0.1149
Test Summary - Avg reward: 6.509±3.040, revenue_rate: 0.3285±0.0488, efficiency: 0.1299, completion_rate: 0.3931, distance: 5.9582, memory: 0.0622, power: 0.1596
Load Balance - Avg balance score: -0.6270±0.2927
Task Distribution by Satellite:
  Satellite 1: 1240 tasks (32.22%)
  Satellite 2: 1077 tasks (27.99%)
  Satellite 3: 1531 tasks (39.79%)
✅ 验证完成 - Epoch 3, reward: 6.509, revenue_rate: 0.3285, distance: 5.9582, memory: 0.0622, power: 0.1596
  ✅ 训练验证差距正常: 0.6004
训练完成

开始测试模型...
Test Batch 4/4, reward: 3.844, revenue_rate: 0.2176, efficiency: 0.0631, distance: 4.3251, memory: 0.1024, power: 0.1052
Test Summary - Avg reward: 6.624±2.829, revenue_rate: 0.3182±0.0486, efficiency: 0.1182, completion_rate: 0.3689, distance: 5.4460, memory: 0.0467, power: 0.1520
Load Balance - Avg balance score: -0.6125±0.3021
Task Distribution by Satellite:
  Satellite 1: 1182 tasks (32.83%)
  Satellite 2: 971 tasks (26.97%)
  Satellite 3: 1447 tasks (40.19%)
测试完成 - 平均奖励: 6.624, 平均星座收益率: 0.3182
✅ 模式 COMPETITIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_11_10_08_12
   平均奖励: 6.6235
   收益率: 0.3182

🚀 [3/3] 开始训练模式: HYBRID

============================================================
开始训练星座模式: HYBRID
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: hybrid
verbose: True
2025_08_11_10_16_27
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 3.7452, Loss: 21.8996, Revenue: 0.4493, LoadBalance: -1.0827, Tasks: [S0:798(43.8%), S1:251(13.8%), S2:775(42.5%)], ActorGrad: 84.5887, CriticGrad: 162.6860, Advantage: μ=0.930, σ=1.527, range=[-1.63, 4.58]

📊 Epoch 1 训练统计:
  平均奖励: 8.2842
  平均损失: 45.6706
  平均收益率: 0.3181
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 6.251, revenue_rate: 0.2460, efficiency: 0.0738, distance: 4.2706, memory: 0.0533, power: 0.1179
Test Summary - Avg reward: 7.563±4.560, revenue_rate: 0.2966±0.0379, efficiency: 0.1057, completion_rate: 0.3557, distance: 5.2325, memory: 0.0352, power: 0.1437
Load Balance - Avg balance score: -0.5900±0.3317
Task Distribution by Satellite:
  Satellite 1: 1075 tasks (30.93%)
  Satellite 2: 1189 tasks (34.21%)
  Satellite 3: 1212 tasks (34.87%)
✅ 验证完成 - Epoch 1, reward: 7.563, revenue_rate: 0.2966, distance: 5.2325, memory: 0.0352, power: 0.1437
  ✅ 训练验证差距正常: 0.7213
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_11_10_16_27 (验证集奖励: 7.5629)

开始训练 Epoch 2/3
Batch 0: Reward: 7.3828, Loss: 27.5375, Revenue: 0.2692, LoadBalance: -0.5416, Tasks: [S0:329(33.2%), S1:329(33.2%), S2:334(33.7%)], ActorGrad: 37.0171, CriticGrad: 42.9839, Advantage: μ=1.088, σ=1.416, range=[-1.49, 4.24]

📊 Epoch 2 训练统计:
  平均奖励: 8.7160
  平均损失: 40.0818
  平均收益率: 0.3147
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 7.094, revenue_rate: 0.2897, efficiency: 0.0927, distance: 3.9497, memory: -0.0163, power: 0.1138
Test Summary - Avg reward: 8.565±4.869, revenue_rate: 0.3361±0.0500, efficiency: 0.1346, completion_rate: 0.3986, distance: 5.9307, memory: 0.0403, power: 0.1611
Load Balance - Avg balance score: -0.5558±0.2766
Task Distribution by Satellite:
  Satellite 1: 1464 tasks (37.54%)
  Satellite 2: 1256 tasks (32.21%)
  Satellite 3: 1180 tasks (30.26%)
✅ 验证完成 - Epoch 2, reward: 8.565, revenue_rate: 0.3361, distance: 5.9307, memory: 0.0403, power: 0.1611
  ✅ 训练验证差距正常: 0.1515
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_11_10_16_27 (验证集奖励: 8.5645)

开始训练 Epoch 3/3
Batch 0: Reward: 9.7538, Loss: 45.1536, Revenue: 0.3361, LoadBalance: -0.4872, Tasks: [S0:445(36.6%), S1:387(31.8%), S2:384(31.6%)], ActorGrad: 54.4883, CriticGrad: 62.1801, Advantage: μ=1.444, σ=1.036, range=[-0.21, 3.19]

📊 Epoch 3 训练统计:
  平均奖励: 8.8919
  平均损失: 40.1701
  平均收益率: 0.3130
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 6.816, revenue_rate: 0.2730, efficiency: 0.0922, distance: 4.3308, memory: 0.0618, power: 0.1285
Test Summary - Avg reward: 8.211±4.001, revenue_rate: 0.2894±0.0452, efficiency: 0.0997, completion_rate: 0.3421, distance: 4.9747, memory: 0.0454, power: 0.1388
Load Balance - Avg balance score: -0.5360±0.2558
Task Distribution by Satellite:
  Satellite 1: 1107 tasks (33.22%)
  Satellite 2: 1090 tasks (32.71%)
  Satellite 3: 1135 tasks (34.06%)
✅ 验证完成 - Epoch 3, reward: 8.211, revenue_rate: 0.2894, distance: 4.9747, memory: 0.0454, power: 0.1388
  ✅ 训练验证差距正常: 0.6812
训练完成

开始测试模型...
Test Batch 4/4, reward: 6.848, revenue_rate: 0.2712, efficiency: 0.0949, distance: 5.2364, memory: 0.1287, power: 0.1424
Test Summary - Avg reward: 9.091±4.641, revenue_rate: 0.3218±0.0437, efficiency: 0.1197, completion_rate: 0.3710, distance: 5.4649, memory: 0.0346, power: 0.1526
Load Balance - Avg balance score: -0.5104±0.2896
Task Distribution by Satellite:
  Satellite 1: 1191 tasks (32.86%)
  Satellite 2: 1135 tasks (31.32%)
  Satellite 3: 1298 tasks (35.82%)
测试完成 - 平均奖励: 9.091, 平均星座收益率: 0.3218
✅ 模式 HYBRID 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_11_10_16_27
   平均奖励: 9.0908
   收益率: 0.3218

================================================================================
🎯 多模式训练总结
================================================================================
✅ COOPERATIVE: 奖励=8.8764, 收益率=0.3062
✅ COMPETITIVE: 奖励=6.6235, 收益率=0.3182
✅ HYBRID: 奖励=9.0908, 收益率=0.3218

🏆 最佳模式: HYBRID
   最高奖励: 9.0908
   对应收益率: 0.3218
   模型路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_11_10_16_27

🎉 所有模式训练完成！
