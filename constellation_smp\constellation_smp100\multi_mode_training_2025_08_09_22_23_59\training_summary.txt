🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_09_22_23_59
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_09_22_29_56
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 6.6338, Loss: 41.4576, Revenue: 0.4350, LoadBalance: 0.4674, Tasks: [S0:746(44.0%), S1:241(14.2%), S2:709(41.8%)], ActorGrad: 64.2969, CriticGrad: 293.1428, Advantage: μ=1.527, σ=0.905, range=[-0.45, 3.98]
Epoch 1, Batch 50/3125, loss: 36.808↓, reward: 8.559↑, critic_reward: 3.586, revenue_rate: 0.5819, distance: 10.7644, memory: 0.0383, power: 0.2780, lr: 0.000050, took: 368.974s
Batch 50: Reward: 9.6581, Loss: 41.0481, Revenue: 0.6530, LoadBalance: 0.0541, Tasks: [S0:873(35.4%), S1:15(0.6%), S2:1576(64.0%)], ActorGrad: 57.2088, CriticGrad: 62.2830, Advantage: μ=1.563, σ=0.839, range=[-0.09, 3.28]
Epoch 1, Batch 100/3125, loss: 38.913↑, reward: 9.639↑, critic_reward: 4.285, revenue_rate: 0.6244, distance: 11.1483, memory: 0.0234, power: 0.2968, lr: 0.000050, took: 372.138s
Batch 100: Reward: 10.7726, Loss: 48.5866, Revenue: 0.6453, LoadBalance: 0.1062, Tasks: [S0:1351(58.6%), S1:24(1.0%), S2:929(40.3%)], ActorGrad: 50.6951, CriticGrad: 81.4734, Advantage: μ=1.623, σ=0.713, range=[0.02, 2.98]
Epoch 1, Batch 150/3125, loss: 38.404↓, reward: 9.792↓, critic_reward: 4.472, revenue_rate: 0.6317, distance: 11.2325, memory: 0.0202, power: 0.2995, lr: 0.000050, took: 370.872s
Batch 150: Reward: 9.2065, Loss: 26.9386, Revenue: 0.6394, LoadBalance: 0.0214, Tasks: [S0:734(31.9%), S1:7(0.3%), S2:1563(67.8%)], ActorGrad: 37.6722, CriticGrad: 54.3824, Advantage: μ=1.540, σ=0.882, range=[0.03, 3.83]
Epoch 1, Batch 200/3125, loss: 34.940↑, reward: 9.625↑, critic_reward: 4.619, revenue_rate: 0.6426, distance: 11.4342, memory: 0.0275, power: 0.3056, lr: 0.000050, took: 381.482s
Batch 200: Reward: 9.3044, Loss: 32.8661, Revenue: 0.6292, LoadBalance: 0.0679, Tasks: [S0:847(37.3%), S1:14(0.6%), S2:1411(62.1%)], ActorGrad: 52.4682, CriticGrad: 53.5316, Advantage: μ=1.442, σ=1.039, range=[-0.99, 3.72]
Epoch 1, Batch 250/3125, loss: 34.720↑, reward: 9.744↑, critic_reward: 4.764, revenue_rate: 0.6408, distance: 11.3316, memory: 0.0282, power: 0.3032, lr: 0.000050, took: 377.000s
Batch 250: Reward: 9.6500, Loss: 29.4197, Revenue: 0.6279, LoadBalance: 0.0217, Tasks: [S0:690(29.9%), S1:12(0.5%), S2:1602(69.5%)], ActorGrad: 40.2533, CriticGrad: 59.4376, Advantage: μ=1.558, σ=0.849, range=[-1.05, 3.45]
Epoch 1, Batch 300/3125, loss: 31.766↑, reward: 9.538↑, critic_reward: 4.870, revenue_rate: 0.6403, distance: 11.4550, memory: 0.0239, power: 0.3045, lr: 0.000050, took: 378.977s
Batch 300: Reward: 9.6137, Loss: 27.8545, Revenue: 0.6188, LoadBalance: 0.0638, Tasks: [S0:861(37.9%), S1:8(0.4%), S2:1403(61.8%)], ActorGrad: 35.1644, CriticGrad: 58.5366, Advantage: μ=1.522, σ=0.913, range=[0.21, 3.08]
Epoch 1, Batch 350/3125, loss: 34.236↑, reward: 10.022↑, critic_reward: 4.987, revenue_rate: 0.6412, distance: 11.2837, memory: 0.0193, power: 0.3042, lr: 0.000050, took: 373.377s
Batch 350: Reward: 9.8551, Loss: 32.0734, Revenue: 0.6577, LoadBalance: 0.1035, Tasks: [S0:1049(43.7%), S1:3(0.1%), S2:1348(56.2%)], ActorGrad: 40.8089, CriticGrad: 59.1601, Advantage: μ=1.513, σ=0.928, range=[-0.32, 3.33]
Epoch 1, Batch 400/3125, loss: 33.729↓, reward: 10.045↓, critic_reward: 5.113, revenue_rate: 0.6416, distance: 11.2378, memory: 0.0193, power: 0.3039, lr: 0.000050, took: 369.477s
Batch 400: Reward: 9.9031, Loss: 32.8755, Revenue: 0.6361, LoadBalance: 0.1309, Tasks: [S0:1173(50.2%), S1:8(0.3%), S2:1155(49.4%)], ActorGrad: 40.1125, CriticGrad: 59.4608, Advantage: μ=1.471, σ=0.996, range=[-0.34, 3.24]
Epoch 1, Batch 450/3125, loss: 32.746↓, reward: 10.056↓, critic_reward: 5.185, revenue_rate: 0.6431, distance: 11.2774, memory: 0.0195, power: 0.3049, lr: 0.000050, took: 372.273s
Batch 450: Reward: 9.9157, Loss: 29.9486, Revenue: 0.6496, LoadBalance: 0.0661, Tasks: [S0:1489(62.0%), S1:10(0.4%), S2:901(37.5%)], ActorGrad: 30.7384, CriticGrad: 58.0613, Advantage: μ=1.505, σ=0.942, range=[-0.43, 3.46]
Epoch 1, Batch 500/3125, loss: 31.743↓, reward: 9.989↓, critic_reward: 5.260, revenue_rate: 0.6452, distance: 11.3413, memory: 0.0199, power: 0.3056, lr: 0.000050, took: 375.191s
Batch 500: Reward: 9.8260, Loss: 27.7529, Revenue: 0.6348, LoadBalance: 0.1204, Tasks: [S0:1194(52.6%), S1:9(0.4%), S2:1069(47.1%)], ActorGrad: 36.5635, CriticGrad: 52.0295, Advantage: μ=1.476, σ=0.989, range=[-1.52, 3.67]
Epoch 1, Batch 550/3125, loss: 30.799↑, reward: 10.054↓, critic_reward: 5.362, revenue_rate: 0.6482, distance: 11.3314, memory: 0.0176, power: 0.3076, lr: 0.000050, took: 374.254s
Batch 550: Reward: 10.3888, Loss: 34.4280, Revenue: 0.6263, LoadBalance: 0.0526, Tasks: [S0:818(36.0%), S1:7(0.3%), S2:1447(63.7%)], ActorGrad: 28.7347, CriticGrad: 58.1286, Advantage: μ=1.493, σ=0.962, range=[-0.14, 3.64]
Epoch 1, Batch 600/3125, loss: 29.855↓, reward: 9.964↓, critic_reward: 5.463, revenue_rate: 0.6511, distance: 11.3890, memory: 0.0228, power: 0.3098, lr: 0.000050, took: 376.963s
Batch 600: Reward: 8.7195, Loss: 24.1337, Revenue: 0.6565, LoadBalance: 0.1172, Tasks: [S0:1178(45.4%), S1:6(0.2%), S2:1408(54.3%)], ActorGrad: 36.7930, CriticGrad: 37.3636, Advantage: μ=1.209, σ=1.310, range=[-1.49, 4.70]
Epoch 1, Batch 650/3125, loss: 29.799↑, reward: 10.015↑, critic_reward: 5.522, revenue_rate: 0.6482, distance: 11.4160, memory: 0.0207, power: 0.3074, lr: 0.000050, took: 377.540s
Batch 650: Reward: 9.9086, Loss: 28.1154, Revenue: 0.6583, LoadBalance: 0.0486, Tasks: [S0:1551(63.8%), S1:8(0.3%), S2:873(35.9%)], ActorGrad: 32.8049, CriticGrad: 48.2773, Advantage: μ=1.393, σ=1.105, range=[-0.70, 3.48]
Epoch 1, Batch 700/3125, loss: 29.427↑, reward: 10.117↑, critic_reward: 5.646, revenue_rate: 0.6488, distance: 11.3538, memory: 0.0174, power: 0.3063, lr: 0.000050, took: 376.577s
Batch 700: Reward: 9.3238, Loss: 20.7420, Revenue: 0.6465, LoadBalance: 0.0097, Tasks: [S0:710(30.0%), S1:5(0.2%), S2:1653(69.8%)], ActorGrad: 52.6427, CriticGrad: 41.7140, Advantage: μ=1.404, σ=1.091, range=[-0.78, 3.52]
Epoch 1, Batch 750/3125, loss: 26.098↑, reward: 9.835↑, critic_reward: 5.734, revenue_rate: 0.6594, distance: 11.6155, memory: 0.0209, power: 0.3129, lr: 0.000050, took: 384.165s
Batch 750: Reward: 10.3202, Loss: 30.1869, Revenue: 0.6458, LoadBalance: 0.0224, Tasks: [S0:745(32.8%), S1:1(0.0%), S2:1526(67.2%)], ActorGrad: 27.3770, CriticGrad: 51.9165, Advantage: μ=1.490, σ=0.967, range=[-0.65, 3.19]
Epoch 1, Batch 800/3125, loss: 26.982↑, reward: 10.046↑, critic_reward: 5.813, revenue_rate: 0.6495, distance: 11.3376, memory: 0.0191, power: 0.3075, lr: 0.000050, took: 374.342s
Batch 800: Reward: 9.9577, Loss: 27.4117, Revenue: 0.6508, LoadBalance: 0.1251, Tasks: [S0:1114(47.0%), S1:6(0.3%), S2:1248(52.7%)], ActorGrad: 32.3040, CriticGrad: 50.1959, Advantage: μ=1.394, σ=1.105, range=[-0.64, 4.87]
Epoch 1, Batch 850/3125, loss: 27.753↓, reward: 10.216↓, critic_reward: 5.887, revenue_rate: 0.6482, distance: 11.3331, memory: 0.0162, power: 0.3065, lr: 0.000050, took: 375.414s
Batch 850: Reward: 10.8166, Loss: 32.8376, Revenue: 0.6441, LoadBalance: 0.1127, Tasks: [S0:1027(45.2%), S1:1(0.0%), S2:1244(54.8%)], ActorGrad: 35.3714, CriticGrad: 55.4387, Advantage: μ=1.472, σ=0.994, range=[-0.45, 3.22]
Epoch 1, Batch 900/3125, loss: 26.820↓, reward: 10.167↓, critic_reward: 5.965, revenue_rate: 0.6484, distance: 11.3041, memory: 0.0164, power: 0.3062, lr: 0.000050, took: 370.681s
Batch 900: Reward: 10.3052, Loss: 28.6218, Revenue: 0.6716, LoadBalance: 0.0746, Tasks: [S0:953(38.7%), S1:7(0.3%), S2:1504(61.0%)], ActorGrad: 35.6942, CriticGrad: 51.6437, Advantage: μ=1.389, σ=1.112, range=[-0.46, 4.27]
Epoch 1, Batch 950/3125, loss: 24.091↓, reward: 9.998↓, critic_reward: 6.067, revenue_rate: 0.6531, distance: 11.4243, memory: 0.0184, power: 0.3102, lr: 0.000050, took: 378.721s
Batch 950: Reward: 9.0305, Loss: 17.3491, Revenue: 0.6584, LoadBalance: 0.0111, Tasks: [S0:750(30.0%), S1:4(0.2%), S2:1742(69.8%)], ActorGrad: 36.6248, CriticGrad: 32.5255, Advantage: μ=1.174, σ=1.343, range=[-1.54, 5.16]
Epoch 1, Batch 1000/3125, loss: 22.769↑, reward: 9.732↑, critic_reward: 6.113, revenue_rate: 0.6633, distance: 11.7307, memory: 0.0222, power: 0.3155, lr: 0.000050, took: 388.734s
Batch 1000: Reward: 9.7940, Loss: 18.6224, Revenue: 0.6692, LoadBalance: 0.0028, Tasks: [S0:549(22.6%), S1:1(0.0%), S2:1882(77.4%)], ActorGrad: 23.8225, CriticGrad: 43.0850, Advantage: μ=1.485, σ=0.974, range=[-0.81, 3.42]
Epoch 1, Batch 1050/3125, loss: 21.165↑, reward: 9.684↓, critic_reward: 6.183, revenue_rate: 0.6676, distance: 11.8476, memory: 0.0248, power: 0.3176, lr: 0.000050, took: 394.830s
Batch 1050: Reward: 9.8745, Loss: 16.8327, Revenue: 0.6671, LoadBalance: 0.0000, Tasks: [S0:490(20.4%), S1:1(0.0%), S2:1909(79.5%)], ActorGrad: 26.2965, CriticGrad: 43.7241, Advantage: μ=1.595, σ=0.775, range=[-0.19, 3.48]
Epoch 1, Batch 1100/3125, loss: 20.563↓, reward: 9.674↓, critic_reward: 6.280, revenue_rate: 0.6751, distance: 11.9408, memory: 0.0263, power: 0.3207, lr: 0.000050, took: 396.455s
Batch 1100: Reward: 10.2785, Loss: 21.5558, Revenue: 0.6733, LoadBalance: 0.0000, Tasks: [S0:340(14.2%), S1:2(0.1%), S2:2058(85.8%)], ActorGrad: 24.7442, CriticGrad: 46.2463, Advantage: μ=1.428, σ=1.058, range=[-1.14, 3.17]
Epoch 1, Batch 1150/3125, loss: 20.230↑, reward: 9.627↓, critic_reward: 6.388, revenue_rate: 0.6835, distance: 12.1499, memory: 0.0329, power: 0.3247, lr: 0.000050, took: 407.237s
Batch 1150: Reward: 9.7761, Loss: 22.7474, Revenue: 0.6789, LoadBalance: 0.0000, Tasks: [S0:314(12.9%), S1:0(0.0%), S2:2118(87.1%)], ActorGrad: 24.0552, CriticGrad: 41.1583, Advantage: μ=1.277, σ=1.242, range=[-1.63, 3.27]
Epoch 1, Batch 1200/3125, loss: 23.487↑, reward: 9.743↑, critic_reward: 6.423, revenue_rate: 0.7492, distance: 13.8098, memory: 0.0658, power: 0.3600, lr: 0.000050, took: 467.152s
Batch 1200: Reward: 11.3171, Loss: 36.8831, Revenue: 0.7893, LoadBalance: 0.0000, Tasks: [S0:75(2.5%), S1:4(0.1%), S2:2865(97.3%)], ActorGrad: 21.9329, CriticGrad: 59.3792, Advantage: μ=1.371, σ=1.133, range=[-1.51, 3.27]
Epoch 1, Batch 1250/3125, loss: 34.841↓, reward: 11.123↑, critic_reward: 6.563, revenue_rate: 0.8313, distance: 15.0404, memory: 0.1099, power: 0.4025, lr: 0.000050, took: 497.286s
Batch 1250: Reward: 11.2172, Loss: 37.1620, Revenue: 0.8483, LoadBalance: 0.0000, Tasks: [S0:15(0.5%), S1:6(0.2%), S2:3115(99.3%)], ActorGrad: 22.0772, CriticGrad: 57.3363, Advantage: μ=1.350, σ=1.160, range=[-2.14, 3.31]
Epoch 1, Batch 1300/3125, loss: 37.330↑, reward: 11.495↑, critic_reward: 6.655, revenue_rate: 0.8461, distance: 15.1547, memory: 0.1152, power: 0.4101, lr: 0.000050, took: 493.034s
Batch 1300: Reward: 10.8573, Loss: 25.5280, Revenue: 0.8322, LoadBalance: 0.0000, Tasks: [S0:25(0.8%), S1:3(0.1%), S2:3076(99.1%)], ActorGrad: 22.1308, CriticGrad: 49.7328, Advantage: μ=1.371, σ=1.133, range=[-0.89, 3.82]
Epoch 1, Batch 1350/3125, loss: 35.495↓, reward: 11.506↓, critic_reward: 6.700, revenue_rate: 0.8524, distance: 15.2406, memory: 0.1208, power: 0.4143, lr: 0.000050, took: 494.721s
Batch 1350: Reward: 12.1970, Loss: 41.2285, Revenue: 0.8651, LoadBalance: 0.0000, Tasks: [S0:12(0.4%), S1:6(0.2%), S2:3150(99.4%)], ActorGrad: 21.3629, CriticGrad: 68.6003, Advantage: μ=1.546, σ=0.871, range=[-0.57, 3.22]
Epoch 1, Batch 1400/3125, loss: 37.325↓, reward: 11.800↓, critic_reward: 6.795, revenue_rate: 0.8549, distance: 15.0892, memory: 0.1209, power: 0.4143, lr: 0.000050, took: 490.820s
Batch 1400: Reward: 11.9001, Loss: 39.5921, Revenue: 0.8508, LoadBalance: 0.0000, Tasks: [S0:17(0.6%), S1:2(0.1%), S2:3021(99.4%)], ActorGrad: 20.9395, CriticGrad: 61.2453, Advantage: μ=1.476, σ=0.989, range=[-0.37, 3.18]
Epoch 1, Batch 1450/3125, loss: 36.516↓, reward: 11.695↓, critic_reward: 6.871, revenue_rate: 0.8574, distance: 15.1929, memory: 0.1238, power: 0.4163, lr: 0.000050, took: 493.516s
Batch 1450: Reward: 12.6943, Loss: 45.6974, Revenue: 0.8554, LoadBalance: 0.0000, Tasks: [S0:12(0.4%), S1:7(0.2%), S2:3053(99.4%)], ActorGrad: 21.3106, CriticGrad: 71.4342, Advantage: μ=1.524, σ=0.910, range=[-0.37, 3.24]
Epoch 1, Batch 1500/3125, loss: 35.871↓, reward: 11.947↓, critic_reward: 6.978, revenue_rate: 0.8605, distance: 15.1649, memory: 0.1214, power: 0.4176, lr: 0.000050, took: 490.769s
Batch 1500: Reward: 11.3899, Loss: 29.5387, Revenue: 0.8394, LoadBalance: 0.0000, Tasks: [S0:10(0.3%), S1:9(0.3%), S2:3053(99.4%)], ActorGrad: 22.9231, CriticGrad: 54.9421, Advantage: μ=1.524, σ=0.911, range=[-0.35, 3.30]
Epoch 1, Batch 1550/3125, loss: 35.443↑, reward: 11.880↑, critic_reward: 7.022, revenue_rate: 0.8609, distance: 15.1519, memory: 0.1243, power: 0.4186, lr: 0.000050, took: 493.132s
Batch 1550: Reward: 11.6449, Loss: 32.0122, Revenue: 0.8707, LoadBalance: 0.0000, Tasks: [S0:12(0.4%), S1:3(0.1%), S2:3121(99.5%)], ActorGrad: 19.9474, CriticGrad: 57.0157, Advantage: μ=1.419, σ=1.071, range=[-0.71, 3.62]
Epoch 1, Batch 1600/3125, loss: 33.346↑, reward: 11.709↑, critic_reward: 7.094, revenue_rate: 0.8628, distance: 15.2674, memory: 0.1249, power: 0.4178, lr: 0.000050, took: 492.442s
Batch 1600: Reward: 11.7953, Loss: 37.8657, Revenue: 0.8576, LoadBalance: 0.0000, Tasks: [S0:12(0.4%), S1:6(0.2%), S2:3150(99.4%)], ActorGrad: 20.5528, CriticGrad: 56.9880, Advantage: μ=1.363, σ=1.144, range=[-1.38, 3.74]
Epoch 1, Batch 1650/3125, loss: 33.801↓, reward: 11.877↑, critic_reward: 7.178, revenue_rate: 0.8615, distance: 15.1776, memory: 0.1247, power: 0.4180, lr: 0.000050, took: 492.013s
Batch 1650: Reward: 11.1488, Loss: 30.6716, Revenue: 0.8506, LoadBalance: 0.0000, Tasks: [S0:8(0.3%), S1:7(0.2%), S2:3121(99.5%)], ActorGrad: 17.5106, CriticGrad: 50.0215, Advantage: μ=1.273, σ=1.246, range=[-1.60, 3.80]
Epoch 1, Batch 1700/3125, loss: 33.529↑, reward: 11.911↑, critic_reward: 7.252, revenue_rate: 0.8653, distance: 15.2583, memory: 0.1251, power: 0.4197, lr: 0.000050, took: 490.532s
Batch 1700: Reward: 12.5503, Loss: 37.3346, Revenue: 0.8729, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:2(0.1%), S2:3065(99.8%)], ActorGrad: 19.5981, CriticGrad: 65.8142, Advantage: μ=1.528, σ=0.903, range=[-0.71, 3.82]
Epoch 1, Batch 1750/3125, loss: 34.494↓, reward: 12.061↓, critic_reward: 7.302, revenue_rate: 0.8647, distance: 15.0799, memory: 0.1271, power: 0.4188, lr: 0.000050, took: 487.538s
Batch 1750: Reward: 11.5449, Loss: 22.5457, Revenue: 0.8747, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:2(0.1%), S2:3161(99.8%)], ActorGrad: 20.6364, CriticGrad: 53.3857, Advantage: μ=1.581, σ=0.803, range=[0.28, 3.87]
Epoch 1, Batch 1800/3125, loss: 31.323↑, reward: 11.837↑, critic_reward: 7.425, revenue_rate: 0.8663, distance: 15.2749, memory: 0.1268, power: 0.4210, lr: 0.000050, took: 493.368s
Batch 1800: Reward: 11.7407, Loss: 27.9010, Revenue: 0.8718, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:3(0.1%), S2:3162(99.8%)], ActorGrad: 20.3008, CriticGrad: 54.5388, Advantage: μ=1.429, σ=1.057, range=[-0.18, 3.37]
Epoch 1, Batch 1850/3125, loss: 29.401↓, reward: 11.765↑, critic_reward: 7.541, revenue_rate: 0.8678, distance: 15.3170, memory: 0.1273, power: 0.4218, lr: 0.000050, took: 490.921s
Batch 1850: Reward: 12.5369, Loss: 37.1176, Revenue: 0.8782, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:1(0.0%), S2:3067(99.8%)], ActorGrad: 18.6314, CriticGrad: 60.9170, Advantage: μ=1.364, σ=1.143, range=[-0.43, 4.11]
Epoch 1, Batch 1900/3125, loss: 29.484↓, reward: 11.886↓, critic_reward: 7.681, revenue_rate: 0.8689, distance: 15.2707, memory: 0.1282, power: 0.4219, lr: 0.000050, took: 490.247s
Batch 1900: Reward: 12.8106, Loss: 34.5123, Revenue: 0.8727, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:1(0.0%), S2:3066(99.8%)], ActorGrad: 17.9904, CriticGrad: 66.1416, Advantage: μ=1.580, σ=0.805, range=[-0.44, 3.19]
Epoch 1, Batch 1950/3125, loss: 29.288↑, reward: 11.952↑, critic_reward: 7.712, revenue_rate: 0.8666, distance: 15.1902, memory: 0.1265, power: 0.4207, lr: 0.000050, took: 488.816s
Batch 1950: Reward: 12.6285, Loss: 29.4163, Revenue: 0.8842, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3133(99.9%)], ActorGrad: 18.5239, CriticGrad: 59.7956, Advantage: μ=1.487, σ=0.972, range=[-0.89, 3.03]
Epoch 1, Batch 2000/3125, loss: 29.128↓, reward: 11.989↓, critic_reward: 7.786, revenue_rate: 0.8684, distance: 15.2477, memory: 0.1276, power: 0.4220, lr: 0.000050, took: 489.206s
Batch 2000: Reward: 10.5931, Loss: 19.4719, Revenue: 0.8623, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:3(0.1%), S2:3130(99.8%)], ActorGrad: 17.8964, CriticGrad: 35.1655, Advantage: μ=1.131, σ=1.380, range=[-1.49, 4.57]
Epoch 1, Batch 2050/3125, loss: 27.400↑, reward: 11.848↑, critic_reward: 7.887, revenue_rate: 0.8692, distance: 15.3297, memory: 0.1281, power: 0.4219, lr: 0.000050, took: 488.584s
Batch 2050: Reward: 12.2883, Loss: 31.2864, Revenue: 0.8646, LoadBalance: 0.0000, Tasks: [S0:8(0.3%), S1:3(0.1%), S2:3093(99.6%)], ActorGrad: 21.1756, CriticGrad: 54.9760, Advantage: μ=1.388, σ=1.113, range=[-1.29, 3.59]
Epoch 1, Batch 2100/3125, loss: 27.191↓, reward: 11.960↓, critic_reward: 7.961, revenue_rate: 0.8679, distance: 15.2086, memory: 0.1276, power: 0.4215, lr: 0.000050, took: 491.032s
Batch 2100: Reward: 12.4823, Loss: 38.4859, Revenue: 0.8642, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:1(0.0%), S2:3034(99.8%)], ActorGrad: 18.3049, CriticGrad: 56.2391, Advantage: μ=1.244, σ=1.276, range=[-1.65, 3.98]
Epoch 1, Batch 2150/3125, loss: 27.298↓, reward: 11.975↑, critic_reward: 8.014, revenue_rate: 0.8708, distance: 15.2496, memory: 0.1280, power: 0.4237, lr: 0.000050, took: 499.390s
Batch 2150: Reward: 11.8281, Loss: 25.1283, Revenue: 0.8838, LoadBalance: 0.0000, Tasks: [S0:7(0.2%), S1:3(0.1%), S2:3094(99.7%)], ActorGrad: 19.7522, CriticGrad: 49.6941, Advantage: μ=1.386, σ=1.114, range=[-0.56, 3.92]
Epoch 1, Batch 2200/3125, loss: 28.394↓, reward: 12.136↓, critic_reward: 8.114, revenue_rate: 0.8697, distance: 15.1616, memory: 0.1283, power: 0.4218, lr: 0.000050, took: 486.529s
Batch 2200: Reward: 11.1841, Loss: 20.2918, Revenue: 0.8737, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:4(0.1%), S2:3130(99.8%)], ActorGrad: 20.6418, CriticGrad: 38.8526, Advantage: μ=1.136, σ=1.376, range=[-1.18, 4.25]
Epoch 1, Batch 2250/3125, loss: 26.698↑, reward: 12.120↑, critic_reward: 8.158, revenue_rate: 0.8715, distance: 15.2121, memory: 0.1305, power: 0.4228, lr: 0.000050, took: 487.317s
Batch 2250: Reward: 11.4535, Loss: 19.4106, Revenue: 0.8666, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3101(99.9%)], ActorGrad: 20.6115, CriticGrad: 41.4687, Advantage: μ=1.212, σ=1.308, range=[-2.15, 4.37]
Epoch 1, Batch 2300/3125, loss: 25.171↑, reward: 11.860↑, critic_reward: 8.246, revenue_rate: 0.8707, distance: 15.3437, memory: 0.1282, power: 0.4238, lr: 0.000050, took: 492.349s
Batch 2300: Reward: 12.2178, Loss: 26.3162, Revenue: 0.8844, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3165(99.9%)], ActorGrad: 20.4513, CriticGrad: 51.9559, Advantage: μ=1.348, σ=1.162, range=[-0.42, 3.71]
Epoch 1, Batch 2350/3125, loss: 25.356↑, reward: 12.002↑, critic_reward: 8.354, revenue_rate: 0.8694, distance: 15.2311, memory: 0.1307, power: 0.4214, lr: 0.000050, took: 490.628s
Batch 2350: Reward: 11.5019, Loss: 19.0492, Revenue: 0.8717, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3037(99.9%)], ActorGrad: 21.2725, CriticGrad: 40.7181, Advantage: μ=1.280, σ=1.238, range=[-1.24, 4.16]
Epoch 1, Batch 2400/3125, loss: 24.751↑, reward: 12.112↑, critic_reward: 8.406, revenue_rate: 0.8695, distance: 15.1442, memory: 0.1263, power: 0.4221, lr: 0.000050, took: 486.530s
Batch 2400: Reward: 11.8774, Loss: 22.6081, Revenue: 0.8783, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:2(0.1%), S2:3162(99.8%)], ActorGrad: 21.6622, CriticGrad: 43.0909, Advantage: μ=1.217, σ=1.302, range=[-1.81, 3.92]
Epoch 1, Batch 2450/3125, loss: 23.460↑, reward: 12.000↑, critic_reward: 8.474, revenue_rate: 0.8692, distance: 15.2078, memory: 0.1278, power: 0.4219, lr: 0.000050, took: 488.038s
Batch 2450: Reward: 12.3435, Loss: 35.5851, Revenue: 0.8761, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:5(0.2%), S2:3126(99.7%)], ActorGrad: 20.0639, CriticGrad: 50.4692, Advantage: μ=1.102, σ=1.404, range=[-1.96, 3.20]
Epoch 1, Batch 2500/3125, loss: 24.032↓, reward: 12.089↑, critic_reward: 8.580, revenue_rate: 0.8737, distance: 15.2609, memory: 0.1258, power: 0.4240, lr: 0.000050, took: 487.604s
Batch 2500: Reward: 12.5935, Loss: 26.9711, Revenue: 0.8636, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:1(0.0%), S2:3004(99.9%)], ActorGrad: 17.9481, CriticGrad: 51.2939, Advantage: μ=1.372, σ=1.133, range=[-0.48, 3.85]
Epoch 1, Batch 2550/3125, loss: 22.806↓, reward: 12.071↑, critic_reward: 8.687, revenue_rate: 0.8726, distance: 15.2076, memory: 0.1287, power: 0.4234, lr: 0.000050, took: 488.739s
Batch 2550: Reward: 12.4681, Loss: 22.6681, Revenue: 0.8846, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:1(0.0%), S2:3132(99.9%)], ActorGrad: 19.4470, CriticGrad: 48.3486, Advantage: μ=1.344, σ=1.167, range=[-0.72, 3.53]
Epoch 1, Batch 2600/3125, loss: 21.248↑, reward: 11.956↓, critic_reward: 8.745, revenue_rate: 0.8719, distance: 15.2784, memory: 0.1294, power: 0.4238, lr: 0.000050, took: 486.160s
Batch 2600: Reward: 11.4778, Loss: 17.8955, Revenue: 0.8706, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:1(0.0%), S2:3129(99.8%)], ActorGrad: 18.5498, CriticGrad: 37.1332, Advantage: μ=1.060, σ=1.437, range=[-1.42, 3.18]
Epoch 1, Batch 2650/3125, loss: 21.315↑, reward: 12.026↑, critic_reward: 8.854, revenue_rate: 0.8731, distance: 15.3036, memory: 0.1309, power: 0.4255, lr: 0.000050, took: 490.608s
Batch 2650: Reward: 12.0008, Loss: 15.9623, Revenue: 0.8687, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3038(99.9%)], ActorGrad: 17.8047, CriticGrad: 43.0038, Advantage: μ=1.355, σ=1.154, range=[-0.89, 3.78]
Epoch 1, Batch 2700/3125, loss: 20.135↑, reward: 11.894↓, critic_reward: 8.914, revenue_rate: 0.8722, distance: 15.3102, memory: 0.1314, power: 0.4235, lr: 0.000050, took: 488.776s
Batch 2700: Reward: 11.6451, Loss: 13.5905, Revenue: 0.8654, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3102(99.9%)], ActorGrad: 20.4251, CriticGrad: 33.9380, Advantage: μ=1.210, σ=1.309, range=[-1.03, 4.02]
Epoch 1, Batch 2750/3125, loss: 20.506↑, reward: 12.000↑, critic_reward: 9.033, revenue_rate: 0.8727, distance: 15.2351, memory: 0.1327, power: 0.4245, lr: 0.000050, took: 489.953s
Batch 2750: Reward: 11.4932, Loss: 14.1240, Revenue: 0.8780, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3165(99.9%)], ActorGrad: 19.9195, CriticGrad: 31.7245, Advantage: μ=1.105, σ=1.402, range=[-1.43, 3.95]
Epoch 1, Batch 2800/3125, loss: 20.405↑, reward: 11.979↑, critic_reward: 9.091, revenue_rate: 0.8751, distance: 15.3171, memory: 0.1284, power: 0.4230, lr: 0.000050, took: 489.545s
Batch 2800: Reward: 11.8705, Loss: 18.0435, Revenue: 0.8672, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3069(99.9%)], ActorGrad: 18.4783, CriticGrad: 35.6010, Advantage: μ=1.064, σ=1.434, range=[-1.41, 4.12]
Epoch 1, Batch 2850/3125, loss: 20.084↓, reward: 12.139↑, critic_reward: 9.214, revenue_rate: 0.8740, distance: 15.2382, memory: 0.1304, power: 0.4229, lr: 0.000050, took: 489.495s
Batch 2850: Reward: 11.4687, Loss: 15.1091, Revenue: 0.8604, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:2(0.1%), S2:3163(99.8%)], ActorGrad: 20.6356, CriticGrad: 25.7797, Advantage: μ=0.876, σ=1.560, range=[-1.94, 4.50]
Epoch 1, Batch 2900/3125, loss: 19.059↑, reward: 11.982↑, critic_reward: 9.313, revenue_rate: 0.8733, distance: 15.2701, memory: 0.1314, power: 0.4232, lr: 0.000050, took: 489.273s
Batch 2900: Reward: 12.0816, Loss: 16.9410, Revenue: 0.8778, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:0(0.0%), S2:3165(99.9%)], ActorGrad: 21.7558, CriticGrad: 37.2779, Advantage: μ=1.246, σ=1.274, range=[-1.01, 4.09]
Epoch 1, Batch 2950/3125, loss: 18.371↑, reward: 11.995↓, critic_reward: 9.330, revenue_rate: 0.8738, distance: 15.3053, memory: 0.1311, power: 0.4249, lr: 0.000050, took: 489.569s
Batch 2950: Reward: 12.7756, Loss: 18.1938, Revenue: 0.8740, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:3(0.1%), S2:3101(99.9%)], ActorGrad: 21.2203, CriticGrad: 44.1181, Advantage: μ=1.290, σ=1.228, range=[-1.91, 3.92]
Epoch 1, Batch 3000/3125, loss: 17.481↓, reward: 11.977↓, critic_reward: 9.471, revenue_rate: 0.8727, distance: 15.2573, memory: 0.1283, power: 0.4232, lr: 0.000050, took: 490.102s
Batch 3000: Reward: 11.7063, Loss: 20.6419, Revenue: 0.8600, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:1(0.0%), S2:3036(99.9%)], ActorGrad: 22.6024, CriticGrad: 33.4794, Advantage: μ=1.036, σ=1.456, range=[-1.57, 3.42]
Epoch 1, Batch 3050/3125, loss: 17.308↓, reward: 11.993↑, critic_reward: 9.530, revenue_rate: 0.8730, distance: 15.2508, memory: 0.1286, power: 0.4243, lr: 0.000050, took: 485.272s
Batch 3050: Reward: 12.5915, Loss: 17.6852, Revenue: 0.8752, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:1(0.0%), S2:3068(99.9%)], ActorGrad: 22.6499, CriticGrad: 41.5798, Advantage: μ=1.279, σ=1.239, range=[-0.57, 4.92]
Epoch 1, Batch 3100/3125, loss: 18.029↓, reward: 12.125↓, critic_reward: 9.659, revenue_rate: 0.8744, distance: 15.1886, memory: 0.1302, power: 0.4240, lr: 0.000050, took: 491.396s
Batch 3100: Reward: 11.8968, Loss: 16.8359, Revenue: 0.8761, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:4(0.1%), S2:3129(99.8%)], ActorGrad: 20.4221, CriticGrad: 32.4530, Advantage: μ=0.965, σ=1.505, range=[-3.07, 3.88]

📊 Epoch 1 训练统计:
  平均奖励: 11.1124
  平均损失: 27.9883
  平均收益率: 0.7840
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 10/313, reward: 11.772, revenue_rate: 0.8707, efficiency: 0.8438, distance: 15.2307, memory: 0.1129, power: 0.4137
Test Batch 20/313, reward: 11.964, revenue_rate: 0.8837, efficiency: 0.8567, distance: 15.3270, memory: 0.1502, power: 0.4343
Test Batch 30/313, reward: 10.837, revenue_rate: 0.8786, efficiency: 0.8770, distance: 15.8092, memory: 0.1440, power: 0.4217
Test Batch 40/313, reward: 11.734, revenue_rate: 0.8839, efficiency: 0.8748, distance: 15.6458, memory: 0.1437, power: 0.4291
Test Batch 50/313, reward: 11.962, revenue_rate: 0.8802, efficiency: 0.8448, distance: 15.2183, memory: 0.1508, power: 0.4294
Test Batch 60/313, reward: 11.592, revenue_rate: 0.8671, efficiency: 0.8492, distance: 15.1425, memory: 0.1254, power: 0.4198
Test Batch 70/313, reward: 11.451, revenue_rate: 0.8695, efficiency: 0.8594, distance: 15.3887, memory: 0.1503, power: 0.4144
Test Batch 80/313, reward: 12.413, revenue_rate: 0.8743, efficiency: 0.8472, distance: 14.9624, memory: 0.1300, power: 0.4227
Test Batch 90/313, reward: 12.270, revenue_rate: 0.8736, efficiency: 0.8469, distance: 14.7998, memory: 0.1271, power: 0.4227
Test Batch 100/313, reward: 12.290, revenue_rate: 0.8723, efficiency: 0.8625, distance: 15.0928, memory: 0.1418, power: 0.4310
Test Batch 110/313, reward: 12.609, revenue_rate: 0.8831, efficiency: 0.8475, distance: 14.8949, memory: 0.1252, power: 0.4352
Test Batch 120/313, reward: 11.635, revenue_rate: 0.8630, efficiency: 0.8358, distance: 15.1714, memory: 0.1205, power: 0.4264
Test Batch 130/313, reward: 11.148, revenue_rate: 0.8774, efficiency: 0.8758, distance: 15.7514, memory: 0.1323, power: 0.4221
Test Batch 140/313, reward: 11.077, revenue_rate: 0.8724, efficiency: 0.8449, distance: 15.6683, memory: 0.1273, power: 0.4224
Test Batch 150/313, reward: 12.180, revenue_rate: 0.8778, efficiency: 0.8679, distance: 15.4871, memory: 0.1288, power: 0.4187
Test Batch 160/313, reward: 11.404, revenue_rate: 0.8803, efficiency: 0.8705, distance: 15.5197, memory: 0.1203, power: 0.4237
Test Batch 170/313, reward: 11.797, revenue_rate: 0.8795, efficiency: 0.8787, distance: 15.2984, memory: 0.1294, power: 0.4281
Test Batch 180/313, reward: 12.974, revenue_rate: 0.8759, efficiency: 0.8485, distance: 14.9863, memory: 0.1242, power: 0.4227
Test Batch 190/313, reward: 11.883, revenue_rate: 0.8859, efficiency: 0.8590, distance: 15.5569, memory: 0.1285, power: 0.4264
Test Batch 200/313, reward: 12.562, revenue_rate: 0.8705, efficiency: 0.8256, distance: 14.6544, memory: 0.1361, power: 0.4207
Test Batch 210/313, reward: 12.056, revenue_rate: 0.8863, efficiency: 0.8852, distance: 15.6773, memory: 0.1282, power: 0.4283
Test Batch 220/313, reward: 11.248, revenue_rate: 0.8721, efficiency: 0.8713, distance: 15.4408, memory: 0.1300, power: 0.4373
Test Batch 230/313, reward: 11.340, revenue_rate: 0.8668, efficiency: 0.8310, distance: 14.8789, memory: 0.1203, power: 0.4223
Test Batch 240/313, reward: 12.132, revenue_rate: 0.8714, efficiency: 0.8363, distance: 15.1723, memory: 0.1245, power: 0.4185
Test Batch 250/313, reward: 12.072, revenue_rate: 0.8714, efficiency: 0.8360, distance: 15.1488, memory: 0.1154, power: 0.4275
Test Batch 260/313, reward: 12.491, revenue_rate: 0.8758, efficiency: 0.8484, distance: 15.2224, memory: 0.1306, power: 0.4152
Test Batch 270/313, reward: 11.110, revenue_rate: 0.8715, efficiency: 0.8704, distance: 15.8521, memory: 0.1266, power: 0.4232
Test Batch 280/313, reward: 12.170, revenue_rate: 0.8690, efficiency: 0.8332, distance: 15.0880, memory: 0.1264, power: 0.4233
Test Batch 290/313, reward: 11.391, revenue_rate: 0.8668, efficiency: 0.8481, distance: 15.2798, memory: 0.1193, power: 0.4187
Test Batch 300/313, reward: 12.772, revenue_rate: 0.8787, efficiency: 0.8688, distance: 15.2476, memory: 0.1224, power: 0.4268
Test Batch 310/313, reward: 12.035, revenue_rate: 0.8785, efficiency: 0.8771, distance: 15.5027, memory: 0.1336, power: 0.4266
Test Batch 313/313, reward: 12.928, revenue_rate: 0.8705, efficiency: 0.8260, distance: 14.4382, memory: 0.1356, power: 0.4275
Test Summary - Avg reward: 11.984±3.215, revenue_rate: 0.8768±0.0336, efficiency: 0.8584, completion_rate: 0.9790, distance: 15.2996, memory: 0.1321, power: 0.4262
Load Balance - Avg balance score: -0.7310±0.0054
Task Distribution by Satellite:
  Satellite 1: 241 tasks (0.02%)
  Satellite 2: 162 tasks (0.02%)
  Satellite 3: 969645 tasks (99.96%)
✅ 验证完成 - Epoch 1, reward: 11.984, revenue_rate: 0.8768, distance: 15.2996, memory: 0.1321, power: 0.4262
  ✅ 训练验证差距正常: -0.8711
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_09_22_29_56 (验证集奖励: 11.9835)

开始训练 Epoch 2/3
Batch 0: Reward: 11.5780, Loss: 20.0738, Revenue: 0.8768, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3102(99.9%)], ActorGrad: 20.0473, CriticGrad: 23.3800, Advantage: μ=0.662, σ=1.665, range=[-2.50, 4.88]
Epoch 2, Batch 50/3125, loss: 17.327↓, reward: 11.966↑, critic_reward: 9.713, revenue_rate: 0.8746, distance: 15.2814, memory: 0.1317, power: 0.4264, lr: 0.000050, took: 493.440s
Batch 50: Reward: 11.9588, Loss: 18.0304, Revenue: 0.8837, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3166(99.9%)], ActorGrad: 19.8108, CriticGrad: 30.4229, Advantage: μ=0.924, σ=1.531, range=[-3.40, 3.36]
Epoch 2, Batch 100/3125, loss: 16.516↓, reward: 12.039↓, critic_reward: 9.850, revenue_rate: 0.8734, distance: 15.2356, memory: 0.1311, power: 0.4251, lr: 0.000050, took: 489.198s
Batch 100: Reward: 11.8404, Loss: 16.4632, Revenue: 0.8711, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:3(0.1%), S2:3131(99.8%)], ActorGrad: 20.4633, CriticGrad: 30.5299, Advantage: μ=0.993, σ=1.486, range=[-2.25, 4.84]
Epoch 2, Batch 150/3125, loss: 16.486↑, reward: 12.081↑, critic_reward: 9.899, revenue_rate: 0.8750, distance: 15.2911, memory: 0.1297, power: 0.4245, lr: 0.000050, took: 489.467s
Batch 150: Reward: 13.2856, Loss: 24.5303, Revenue: 0.8742, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:0(0.0%), S2:3039(100.0%)], ActorGrad: 20.6682, CriticGrad: 44.3201, Advantage: μ=1.196, σ=1.323, range=[-2.29, 4.49]
Epoch 2, Batch 200/3125, loss: 16.391↓, reward: 12.105↓, critic_reward: 9.981, revenue_rate: 0.8749, distance: 15.2335, memory: 0.1295, power: 0.4255, lr: 0.000050, took: 488.127s
Batch 200: Reward: 11.8413, Loss: 10.6142, Revenue: 0.8775, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:0(0.0%), S2:3069(99.9%)], ActorGrad: 21.3788, CriticGrad: 23.8310, Advantage: μ=0.909, σ=1.540, range=[-2.06, 3.90]
Epoch 2, Batch 250/3125, loss: 16.165↑, reward: 12.152↑, critic_reward: 10.032, revenue_rate: 0.8752, distance: 15.2217, memory: 0.1313, power: 0.4252, lr: 0.000050, took: 488.372s
Batch 250: Reward: 10.8060, Loss: 11.7217, Revenue: 0.8537, LoadBalance: 0.0000, Tasks: [S0:8(0.3%), S1:1(0.0%), S2:3063(99.7%)], ActorGrad: 21.0058, CriticGrad: 11.7196, Advantage: μ=0.286, σ=1.772, range=[-3.17, 3.45]
Epoch 2, Batch 300/3125, loss: 15.155↑, reward: 12.102↑, critic_reward: 10.160, revenue_rate: 0.8731, distance: 15.1508, memory: 0.1315, power: 0.4242, lr: 0.000050, took: 485.862s
Batch 300: Reward: 12.8442, Loss: 18.7647, Revenue: 0.8652, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3038(99.9%)], ActorGrad: 21.8822, CriticGrad: 37.0413, Advantage: μ=1.131, σ=1.381, range=[-1.96, 3.77]
Epoch 2, Batch 350/3125, loss: 15.066↓, reward: 12.104↓, critic_reward: 10.262, revenue_rate: 0.8752, distance: 15.2447, memory: 0.1305, power: 0.4244, lr: 0.000050, took: 489.702s
Batch 350: Reward: 12.1761, Loss: 18.4457, Revenue: 0.8773, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3101(99.9%)], ActorGrad: 20.9457, CriticGrad: 31.3392, Advantage: μ=0.951, σ=1.514, range=[-2.21, 4.22]
Epoch 2, Batch 400/3125, loss: 14.146↓, reward: 11.806↓, critic_reward: 10.278, revenue_rate: 0.8734, distance: 15.3235, memory: 0.1300, power: 0.4246, lr: 0.000050, took: 490.988s
Batch 400: Reward: 12.0027, Loss: 12.2333, Revenue: 0.8703, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3069(99.9%)], ActorGrad: 21.4930, CriticGrad: 24.6060, Advantage: μ=0.879, σ=1.558, range=[-3.35, 3.17]
Epoch 2, Batch 450/3125, loss: 13.617↑, reward: 11.913↓, critic_reward: 10.378, revenue_rate: 0.8753, distance: 15.3357, memory: 0.1289, power: 0.4243, lr: 0.000050, took: 487.112s
Batch 450: Reward: 11.7166, Loss: 12.3709, Revenue: 0.8681, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:3(0.1%), S2:3069(99.9%)], ActorGrad: 20.0198, CriticGrad: 18.1570, Advantage: μ=0.598, σ=1.690, range=[-3.18, 4.94]
Epoch 2, Batch 500/3125, loss: 14.194↑, reward: 11.980↑, critic_reward: 10.481, revenue_rate: 0.8733, distance: 15.2886, memory: 0.1302, power: 0.4249, lr: 0.000050, took: 491.656s
Batch 500: Reward: 12.8629, Loss: 14.9539, Revenue: 0.8621, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:0(0.0%), S2:3005(99.9%)], ActorGrad: 17.7170, CriticGrad: 29.0746, Advantage: μ=0.883, σ=1.556, range=[-2.27, 3.41]
Epoch 2, Batch 550/3125, loss: 14.278↓, reward: 11.818↓, critic_reward: 10.643, revenue_rate: 0.8752, distance: 15.3878, memory: 0.1317, power: 0.4246, lr: 0.000050, took: 489.107s
Batch 550: Reward: 12.3901, Loss: 12.5369, Revenue: 0.8846, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:1(0.0%), S2:3103(100.0%)], ActorGrad: 18.9305, CriticGrad: 25.7454, Advantage: μ=0.872, σ=1.562, range=[-2.16, 3.78]
Epoch 2, Batch 600/3125, loss: 13.673↑, reward: 12.079↑, critic_reward: 10.645, revenue_rate: 0.8745, distance: 15.2565, memory: 0.1292, power: 0.4230, lr: 0.000050, took: 490.318s
Batch 600: Reward: 10.8574, Loss: 9.7222, Revenue: 0.8696, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3168(100.0%)], ActorGrad: 19.6766, CriticGrad: 6.9395, Advantage: μ=0.063, σ=1.795, range=[-4.04, 3.17]
Epoch 2, Batch 650/3125, loss: 13.893↑, reward: 12.076↑, critic_reward: 10.713, revenue_rate: 0.8763, distance: 15.3006, memory: 0.1317, power: 0.4253, lr: 0.000050, took: 487.799s
Batch 650: Reward: 11.9964, Loss: 12.0947, Revenue: 0.8790, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3166(99.9%)], ActorGrad: 21.1090, CriticGrad: 14.9071, Advantage: μ=0.561, σ=1.703, range=[-3.12, 4.08]
Epoch 2, Batch 700/3125, loss: 13.011↑, reward: 11.963↑, critic_reward: 10.805, revenue_rate: 0.8741, distance: 15.2567, memory: 0.1307, power: 0.4239, lr: 0.000050, took: 487.933s
Batch 700: Reward: 11.7992, Loss: 11.3029, Revenue: 0.8789, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3102(99.9%)], ActorGrad: 21.0476, CriticGrad: 15.5926, Advantage: μ=0.576, σ=1.698, range=[-3.32, 4.03]
Epoch 2, Batch 750/3125, loss: 13.233↑, reward: 12.101↓, critic_reward: 10.831, revenue_rate: 0.8762, distance: 15.2539, memory: 0.1321, power: 0.4265, lr: 0.000050, took: 489.859s
Batch 750: Reward: 12.5541, Loss: 13.8750, Revenue: 0.8770, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:1(0.0%), S2:3135(100.0%)], ActorGrad: 21.0846, CriticGrad: 21.8549, Advantage: μ=0.794, σ=1.605, range=[-2.10, 4.75]
Epoch 2, Batch 800/3125, loss: 12.997↑, reward: 11.912↑, critic_reward: 10.901, revenue_rate: 0.8748, distance: 15.3281, memory: 0.1318, power: 0.4243, lr: 0.000050, took: 492.012s
Batch 800: Reward: 12.4786, Loss: 8.2907, Revenue: 0.8778, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3101(99.9%)], ActorGrad: 22.4331, CriticGrad: 19.7930, Advantage: μ=0.852, σ=1.573, range=[-1.89, 3.92]
Epoch 2, Batch 850/3125, loss: 12.436↑, reward: 12.013↑, critic_reward: 10.974, revenue_rate: 0.8758, distance: 15.3222, memory: 0.1321, power: 0.4243, lr: 0.000050, took: 491.562s
Batch 850: Reward: 12.7060, Loss: 18.4642, Revenue: 0.8799, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3133(99.9%)], ActorGrad: 22.7485, CriticGrad: 28.1045, Advantage: μ=0.876, σ=1.560, range=[-1.74, 4.75]
Epoch 2, Batch 900/3125, loss: 13.096↓, reward: 12.143↓, critic_reward: 11.022, revenue_rate: 0.8750, distance: 15.2479, memory: 0.1303, power: 0.4248, lr: 0.000050, took: 490.570s
Batch 900: Reward: 12.4630, Loss: 12.6756, Revenue: 0.8720, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:0(0.0%), S2:3071(100.0%)], ActorGrad: 24.0950, CriticGrad: 26.3013, Advantage: μ=0.958, σ=1.509, range=[-1.18, 5.32]
Epoch 2, Batch 950/3125, loss: 12.839↑, reward: 12.002↓, critic_reward: 11.132, revenue_rate: 0.8768, distance: 15.3168, memory: 0.1324, power: 0.4262, lr: 0.000050, took: 494.203s
Batch 950: Reward: 12.2152, Loss: 10.7653, Revenue: 0.8723, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3006(99.9%)], ActorGrad: 25.7626, CriticGrad: 11.4069, Advantage: μ=0.420, σ=1.745, range=[-3.08, 5.41]
Epoch 2, Batch 1000/3125, loss: 12.923↓, reward: 12.095↓, critic_reward: 11.116, revenue_rate: 0.8773, distance: 15.2867, memory: 0.1294, power: 0.4272, lr: 0.000050, took: 488.614s
Batch 1000: Reward: 11.5885, Loss: 14.2652, Revenue: 0.8632, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3072(100.0%)], ActorGrad: 23.6332, CriticGrad: 8.2980, Advantage: μ=0.236, σ=1.780, range=[-3.75, 4.43]
Epoch 2, Batch 1050/3125, loss: 13.484↑, reward: 11.981↑, critic_reward: 11.264, revenue_rate: 0.8738, distance: 15.2917, memory: 0.1300, power: 0.4241, lr: 0.000050, took: 493.624s
Batch 1050: Reward: 10.9347, Loss: 17.4065, Revenue: 0.8852, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3168(100.0%)], ActorGrad: 24.4095, CriticGrad: 11.6579, Advantage: μ=-0.129, σ=1.791, range=[-3.05, 4.28]
Epoch 2, Batch 1100/3125, loss: 12.592↓, reward: 12.016↑, critic_reward: 11.174, revenue_rate: 0.8766, distance: 15.3296, memory: 0.1306, power: 0.4254, lr: 0.000050, took: 490.280s
Batch 1100: Reward: 11.6587, Loss: 8.4264, Revenue: 0.8666, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3038(99.9%)], ActorGrad: 23.6351, CriticGrad: 8.5144, Advantage: μ=0.374, σ=1.755, range=[-2.59, 3.98]
Epoch 2, Batch 1150/3125, loss: 12.415↓, reward: 11.946↑, critic_reward: 11.292, revenue_rate: 0.8749, distance: 15.2746, memory: 0.1303, power: 0.4249, lr: 0.000050, took: 491.031s
Batch 1150: Reward: 11.4924, Loss: 10.9004, Revenue: 0.8796, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:0(0.0%), S2:3167(100.0%)], ActorGrad: 21.8420, CriticGrad: 6.2006, Advantage: μ=-0.066, σ=1.795, range=[-4.93, 2.81]
Epoch 2, Batch 1200/3125, loss: 12.536↑, reward: 12.076↑, critic_reward: 11.346, revenue_rate: 0.8742, distance: 15.2703, memory: 0.1305, power: 0.4256, lr: 0.000050, took: 492.671s
Batch 1200: Reward: 12.2415, Loss: 10.3159, Revenue: 0.8761, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3134(99.9%)], ActorGrad: 22.4887, CriticGrad: 9.6959, Advantage: μ=0.312, σ=1.768, range=[-2.32, 3.94]
Epoch 2, Batch 1250/3125, loss: 12.378↑, reward: 12.100↑, critic_reward: 11.366, revenue_rate: 0.8746, distance: 15.2514, memory: 0.1301, power: 0.4255, lr: 0.000050, took: 491.522s
Batch 1250: Reward: 12.2786, Loss: 9.0689, Revenue: 0.8828, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:3(0.1%), S2:3163(99.8%)], ActorGrad: 22.4895, CriticGrad: 13.1170, Advantage: μ=0.474, σ=1.730, range=[-3.00, 3.45]
Epoch 2, Batch 1300/3125, loss: 12.575↑, reward: 12.140↓, critic_reward: 11.480, revenue_rate: 0.8737, distance: 15.1907, memory: 0.1299, power: 0.4231, lr: 0.000050, took: 487.373s
Batch 1300: Reward: 11.8168, Loss: 13.9750, Revenue: 0.8781, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3134(99.9%)], ActorGrad: 24.0814, CriticGrad: 8.7880, Advantage: μ=0.174, σ=1.787, range=[-3.13, 3.96]
Epoch 2, Batch 1350/3125, loss: 13.406↓, reward: 12.020↑, critic_reward: 11.452, revenue_rate: 0.8756, distance: 15.3001, memory: 0.1324, power: 0.4252, lr: 0.000050, took: 490.463s
Batch 1350: Reward: 11.9339, Loss: 14.7774, Revenue: 0.8798, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3133(99.9%)], ActorGrad: 23.1041, CriticGrad: 14.3337, Advantage: μ=0.390, σ=1.752, range=[-3.04, 4.02]
Epoch 2, Batch 1400/3125, loss: 12.361↓, reward: 12.108↑, critic_reward: 11.529, revenue_rate: 0.8755, distance: 15.2380, memory: 0.1332, power: 0.4254, lr: 0.000050, took: 491.620s
Batch 1400: Reward: 11.6817, Loss: 18.6585, Revenue: 0.8846, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3165(99.9%)], ActorGrad: 25.6088, CriticGrad: 10.0840, Advantage: μ=-0.081, σ=1.794, range=[-4.40, 3.97]
Epoch 2, Batch 1450/3125, loss: 12.733↓, reward: 12.013↑, critic_reward: 11.586, revenue_rate: 0.8747, distance: 15.3054, memory: 0.1326, power: 0.4252, lr: 0.000050, took: 491.144s
Batch 1450: Reward: 10.9867, Loss: 12.5156, Revenue: 0.8722, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:1(0.0%), S2:3167(100.0%)], ActorGrad: 24.5242, CriticGrad: 10.1637, Advantage: μ=-0.164, σ=1.788, range=[-3.81, 3.01]
Epoch 2, Batch 1500/3125, loss: 12.316↓, reward: 11.971↑, critic_reward: 11.604, revenue_rate: 0.8773, distance: 15.3352, memory: 0.1325, power: 0.4253, lr: 0.000050, took: 493.576s
Batch 1500: Reward: 11.6677, Loss: 8.4617, Revenue: 0.8761, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:2(0.1%), S2:3164(99.9%)], ActorGrad: 25.3169, CriticGrad: 7.2708, Advantage: μ=0.128, σ=1.791, range=[-3.65, 3.98]
Epoch 2, Batch 1550/3125, loss: 12.649↑, reward: 12.054↓, critic_reward: 11.582, revenue_rate: 0.8740, distance: 15.1802, memory: 0.1310, power: 0.4235, lr: 0.000050, took: 489.487s
Batch 1550: Reward: 13.5666, Loss: 21.7688, Revenue: 0.8736, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3101(99.9%)], ActorGrad: 25.0720, CriticGrad: 28.8039, Advantage: μ=0.775, σ=1.614, range=[-2.32, 4.03]
Epoch 2, Batch 1600/3125, loss: 11.858↓, reward: 12.000↓, critic_reward: 11.619, revenue_rate: 0.8746, distance: 15.2386, memory: 0.1318, power: 0.4251, lr: 0.000050, took: 493.628s
Batch 1600: Reward: 12.1469, Loss: 9.3034, Revenue: 0.8783, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:2(0.1%), S2:3164(99.9%)], ActorGrad: 24.5297, CriticGrad: 6.6446, Advantage: μ=-0.041, σ=1.796, range=[-3.30, 5.76]
Epoch 2, Batch 1650/3125, loss: 11.532↑, reward: 12.163↑, critic_reward: 11.740, revenue_rate: 0.8756, distance: 15.2171, memory: 0.1305, power: 0.4261, lr: 0.000050, took: 485.799s
Batch 1650: Reward: 12.5043, Loss: 16.3514, Revenue: 0.8770, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:3(0.1%), S2:3164(99.9%)], ActorGrad: 25.6606, CriticGrad: 15.8105, Advantage: μ=0.511, σ=1.720, range=[-2.75, 4.61]
Epoch 2, Batch 1700/3125, loss: 11.923↓, reward: 11.927↓, critic_reward: 11.667, revenue_rate: 0.8749, distance: 15.3082, memory: 0.1299, power: 0.4249, lr: 0.000050, took: 493.454s
Batch 1700: Reward: 12.2937, Loss: 10.8299, Revenue: 0.8759, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3133(99.9%)], ActorGrad: 24.4428, CriticGrad: 6.1711, Advantage: μ=0.176, σ=1.787, range=[-3.56, 3.96]
Epoch 2, Batch 1750/3125, loss: 11.776↑, reward: 12.036↓, critic_reward: 11.676, revenue_rate: 0.8756, distance: 15.2765, memory: 0.1309, power: 0.4256, lr: 0.000050, took: 490.452s
Batch 1750: Reward: 13.5491, Loss: 21.3189, Revenue: 0.8660, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3040(100.0%)], ActorGrad: 24.3515, CriticGrad: 30.3764, Advantage: μ=0.959, σ=1.509, range=[-2.23, 4.41]
Epoch 2, Batch 1800/3125, loss: 11.991↓, reward: 11.955↓, critic_reward: 11.683, revenue_rate: 0.8740, distance: 15.2304, memory: 0.1311, power: 0.4240, lr: 0.000050, took: 490.723s
Batch 1800: Reward: 11.5783, Loss: 9.8474, Revenue: 0.8656, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3104(100.0%)], ActorGrad: 23.0607, CriticGrad: 13.1298, Advantage: μ=-0.204, σ=1.784, range=[-3.17, 3.79]
Epoch 2, Batch 1850/3125, loss: 11.845↑, reward: 11.892↑, critic_reward: 11.633, revenue_rate: 0.8734, distance: 15.2427, memory: 0.1317, power: 0.4256, lr: 0.000050, took: 489.766s
Batch 1850: Reward: 11.7963, Loss: 15.8910, Revenue: 0.8676, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:2(0.1%), S2:3164(99.9%)], ActorGrad: 21.6996, CriticGrad: 11.9531, Advantage: μ=-0.021, σ=1.796, range=[-4.22, 4.77]
Epoch 2, Batch 1900/3125, loss: 12.495↑, reward: 12.095↑, critic_reward: 11.729, revenue_rate: 0.8759, distance: 15.2799, memory: 0.1329, power: 0.4253, lr: 0.000050, took: 494.407s
Batch 1900: Reward: 12.5169, Loss: 9.0159, Revenue: 0.8924, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:0(0.0%), S2:3135(100.0%)], ActorGrad: 24.9210, CriticGrad: 21.5730, Advantage: μ=0.871, σ=1.563, range=[-2.72, 4.68]
Epoch 2, Batch 1950/3125, loss: 11.508↑, reward: 12.028↓, critic_reward: 11.716, revenue_rate: 0.8736, distance: 15.2056, memory: 0.1294, power: 0.4243, lr: 0.000050, took: 488.813s
Batch 1950: Reward: 11.7419, Loss: 12.4022, Revenue: 0.8673, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:1(0.0%), S2:3132(99.9%)], ActorGrad: 24.0218, CriticGrad: 6.5588, Advantage: μ=0.125, σ=1.792, range=[-2.64, 4.97]
Epoch 2, Batch 2000/3125, loss: 12.609↑, reward: 12.003↑, critic_reward: 11.721, revenue_rate: 0.8762, distance: 15.3036, memory: 0.1311, power: 0.4246, lr: 0.000050, took: 490.462s
Batch 2000: Reward: 13.0007, Loss: 16.7639, Revenue: 0.8720, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:1(0.0%), S2:3065(99.8%)], ActorGrad: 23.7292, CriticGrad: 15.8787, Advantage: μ=0.547, σ=1.708, range=[-2.68, 4.08]
Epoch 2, Batch 2050/3125, loss: 12.115↓, reward: 12.017↓, critic_reward: 11.766, revenue_rate: 0.8742, distance: 15.2346, memory: 0.1317, power: 0.4259, lr: 0.000050, took: 493.146s
Batch 2050: Reward: 12.0357, Loss: 19.5143, Revenue: 0.8881, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3166(99.9%)], ActorGrad: 22.7459, CriticGrad: 11.6540, Advantage: μ=0.216, σ=1.783, range=[-3.16, 5.52]
Epoch 2, Batch 2100/3125, loss: 12.080↓, reward: 12.075↑, critic_reward: 11.792, revenue_rate: 0.8767, distance: 15.2651, memory: 0.1319, power: 0.4252, lr: 0.000050, took: 489.603s
Batch 2100: Reward: 11.5548, Loss: 13.8949, Revenue: 0.8739, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:3(0.1%), S2:3130(99.8%)], ActorGrad: 23.7588, CriticGrad: 11.3481, Advantage: μ=-0.113, σ=1.792, range=[-3.89, 3.09]
Epoch 2, Batch 2150/3125, loss: 11.537↓, reward: 11.941↓, critic_reward: 11.815, revenue_rate: 0.8753, distance: 15.3238, memory: 0.1332, power: 0.4255, lr: 0.000050, took: 493.886s
Batch 2150: Reward: 11.6276, Loss: 12.3131, Revenue: 0.8769, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:1(0.0%), S2:3132(99.9%)], ActorGrad: 23.5592, CriticGrad: 8.3682, Advantage: μ=-0.132, σ=1.791, range=[-2.84, 4.42]
Epoch 2, Batch 2200/3125, loss: 12.155↑, reward: 12.009↑, critic_reward: 11.743, revenue_rate: 0.8763, distance: 15.2916, memory: 0.1333, power: 0.4251, lr: 0.000050, took: 489.178s
Batch 2200: Reward: 12.1573, Loss: 18.9626, Revenue: 0.8709, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:2(0.1%), S2:3166(99.9%)], ActorGrad: 22.3017, CriticGrad: 9.5763, Advantage: μ=0.186, σ=1.786, range=[-3.29, 3.68]
Epoch 2, Batch 2250/3125, loss: 11.631↓, reward: 12.163↑, critic_reward: 11.740, revenue_rate: 0.8738, distance: 15.1734, memory: 0.1314, power: 0.4240, lr: 0.000050, took: 492.542s
Batch 2250: Reward: 11.9842, Loss: 10.3435, Revenue: 0.8771, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:0(0.0%), S2:3134(99.9%)], ActorGrad: 24.6686, CriticGrad: 7.6817, Advantage: μ=-0.063, σ=1.795, range=[-4.47, 3.29]
Epoch 2, Batch 2300/3125, loss: 12.425↑, reward: 12.071↑, critic_reward: 11.765, revenue_rate: 0.8748, distance: 15.2902, memory: 0.1323, power: 0.4255, lr: 0.000050, took: 489.566s
Batch 2300: Reward: 11.3814, Loss: 10.7663, Revenue: 0.8776, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:1(0.0%), S2:3167(100.0%)], ActorGrad: 25.9606, CriticGrad: 8.6747, Advantage: μ=-0.199, σ=1.785, range=[-3.95, 3.17]
Epoch 2, Batch 2350/3125, loss: 11.955↑, reward: 12.033↑, critic_reward: 11.775, revenue_rate: 0.8755, distance: 15.3034, memory: 0.1316, power: 0.4253, lr: 0.000050, took: 493.410s
Batch 2350: Reward: 13.0769, Loss: 12.6867, Revenue: 0.8827, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3102(99.9%)], ActorGrad: 26.9060, CriticGrad: 19.4973, Advantage: μ=0.626, σ=1.680, range=[-2.91, 3.82]
Epoch 2, Batch 2400/3125, loss: 12.298↓, reward: 12.041↓, critic_reward: 11.811, revenue_rate: 0.8749, distance: 15.3082, memory: 0.1327, power: 0.4257, lr: 0.000050, took: 494.049s
Batch 2400: Reward: 11.9864, Loss: 8.5865, Revenue: 0.8791, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3166(99.9%)], ActorGrad: 22.8296, CriticGrad: 4.3904, Advantage: μ=0.196, σ=1.785, range=[-3.39, 2.99]
Epoch 2, Batch 2450/3125, loss: 11.341↑, reward: 11.990↓, critic_reward: 11.787, revenue_rate: 0.8758, distance: 15.3428, memory: 0.1305, power: 0.4238, lr: 0.000050, took: 489.243s
Batch 2450: Reward: 11.8949, Loss: 5.7019, Revenue: 0.8691, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:2(0.1%), S2:3164(99.9%)], ActorGrad: 23.8623, CriticGrad: 3.6814, Advantage: μ=-0.002, σ=1.796, range=[-3.23, 5.14]
Epoch 2, Batch 2500/3125, loss: 12.019↑, reward: 11.920↑, critic_reward: 11.776, revenue_rate: 0.8745, distance: 15.2502, memory: 0.1327, power: 0.4245, lr: 0.000050, took: 496.545s
Batch 2500: Reward: 11.5944, Loss: 8.2771, Revenue: 0.8802, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:2(0.1%), S2:3163(99.8%)], ActorGrad: 23.8415, CriticGrad: 7.7642, Advantage: μ=-0.079, σ=1.794, range=[-3.96, 4.46]
Epoch 2, Batch 2550/3125, loss: 12.254↑, reward: 11.984↓, critic_reward: 11.779, revenue_rate: 0.8750, distance: 15.2387, memory: 0.1296, power: 0.4259, lr: 0.000050, took: 495.038s
Batch 2550: Reward: 11.9946, Loss: 8.9822, Revenue: 0.8676, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:0(0.0%), S2:3039(100.0%)], ActorGrad: 22.5327, CriticGrad: 5.1852, Advantage: μ=0.125, σ=1.792, range=[-3.20, 4.60]
Epoch 2, Batch 2600/3125, loss: 12.420↑, reward: 11.946↓, critic_reward: 11.803, revenue_rate: 0.8765, distance: 15.3372, memory: 0.1315, power: 0.4255, lr: 0.000050, took: 496.651s
Batch 2600: Reward: 12.3389, Loss: 7.2718, Revenue: 0.8819, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:4(0.1%), S2:3163(99.8%)], ActorGrad: 26.2451, CriticGrad: 4.5956, Advantage: μ=0.040, σ=1.796, range=[-3.97, 4.34]
Epoch 2, Batch 2650/3125, loss: 11.890↓, reward: 12.091↓, critic_reward: 11.858, revenue_rate: 0.8750, distance: 15.2163, memory: 0.1304, power: 0.4258, lr: 0.000050, took: 489.251s
Batch 2650: Reward: 11.4377, Loss: 11.2510, Revenue: 0.8680, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3133(99.9%)], ActorGrad: 23.1779, CriticGrad: 10.2586, Advantage: μ=-0.153, σ=1.789, range=[-3.92, 2.99]
Epoch 2, Batch 2700/3125, loss: 12.171↑, reward: 12.105↑, critic_reward: 11.778, revenue_rate: 0.8764, distance: 15.3076, memory: 0.1313, power: 0.4263, lr: 0.000050, took: 499.395s
Batch 2700: Reward: 11.6775, Loss: 22.3602, Revenue: 0.8774, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3168(100.0%)], ActorGrad: 25.1506, CriticGrad: 9.6205, Advantage: μ=0.126, σ=1.791, range=[-3.14, 3.19]
Epoch 2, Batch 2750/3125, loss: 12.353↓, reward: 11.925↑, critic_reward: 11.762, revenue_rate: 0.8753, distance: 15.2781, memory: 0.1303, power: 0.4245, lr: 0.000050, took: 507.082s
Batch 2750: Reward: 12.1376, Loss: 13.9584, Revenue: 0.8748, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:3(0.1%), S2:3163(99.8%)], ActorGrad: 25.8149, CriticGrad: 6.8272, Advantage: μ=0.030, σ=1.796, range=[-4.54, 4.19]
Epoch 2, Batch 2800/3125, loss: 12.008↓, reward: 12.183↑, critic_reward: 11.812, revenue_rate: 0.8762, distance: 15.2810, memory: 0.1302, power: 0.4248, lr: 0.000050, took: 512.890s
Batch 2800: Reward: 11.9288, Loss: 5.4723, Revenue: 0.8685, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:0(0.0%), S2:3099(99.8%)], ActorGrad: 27.0019, CriticGrad: 5.2542, Advantage: μ=-0.010, σ=1.796, range=[-4.20, 4.24]
Epoch 2, Batch 2850/3125, loss: 12.032↑, reward: 12.143↓, critic_reward: 11.840, revenue_rate: 0.8744, distance: 15.2032, memory: 0.1294, power: 0.4263, lr: 0.000050, took: 509.216s
Batch 2850: Reward: 12.0207, Loss: 16.2083, Revenue: 0.8742, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:0(0.0%), S2:3167(100.0%)], ActorGrad: 24.2331, CriticGrad: 11.1255, Advantage: μ=0.093, σ=1.794, range=[-2.40, 4.97]
Epoch 2, Batch 2900/3125, loss: 12.454↓, reward: 12.161↓, critic_reward: 11.926, revenue_rate: 0.8766, distance: 15.2191, memory: 0.1329, power: 0.4244, lr: 0.000050, took: 509.849s
Batch 2900: Reward: 11.9240, Loss: 8.1011, Revenue: 0.8747, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3101(99.9%)], ActorGrad: 23.4879, CriticGrad: 5.5624, Advantage: μ=-0.050, σ=1.795, range=[-3.25, 3.39]
Epoch 2, Batch 2950/3125, loss: 11.874↑, reward: 12.026↓, critic_reward: 11.824, revenue_rate: 0.8763, distance: 15.2879, memory: 0.1330, power: 0.4251, lr: 0.000050, took: 512.444s
Batch 2950: Reward: 12.0117, Loss: 13.4268, Revenue: 0.8626, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:5(0.2%), S2:3160(99.7%)], ActorGrad: 24.1064, CriticGrad: 9.8357, Advantage: μ=-0.206, σ=1.784, range=[-4.49, 3.47]
Epoch 2, Batch 3000/3125, loss: 12.128↓, reward: 11.915↓, critic_reward: 11.856, revenue_rate: 0.8733, distance: 15.2591, memory: 0.1281, power: 0.4241, lr: 0.000050, took: 506.971s
Batch 3000: Reward: 12.4270, Loss: 18.2914, Revenue: 0.8765, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:3(0.1%), S2:3163(99.8%)], ActorGrad: 24.6404, CriticGrad: 14.0903, Advantage: μ=0.374, σ=1.755, range=[-4.34, 4.53]
Epoch 2, Batch 3050/3125, loss: 11.683↓, reward: 12.125↑, critic_reward: 11.840, revenue_rate: 0.8745, distance: 15.2157, memory: 0.1295, power: 0.4251, lr: 0.000050, took: 508.553s
Batch 3050: Reward: 12.0657, Loss: 15.5345, Revenue: 0.8656, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:6(0.2%), S2:3157(99.7%)], ActorGrad: 32.7728, CriticGrad: 8.7310, Advantage: μ=-0.003, σ=1.796, range=[-4.18, 3.90]
Epoch 2, Batch 3100/3125, loss: 12.593↓, reward: 12.138↓, critic_reward: 11.890, revenue_rate: 0.8739, distance: 15.2446, memory: 0.1291, power: 0.4250, lr: 0.000050, took: 505.782s
Batch 3100: Reward: 12.4983, Loss: 14.3750, Revenue: 0.8760, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:4(0.1%), S2:3163(99.8%)], ActorGrad: 27.8478, CriticGrad: 16.4893, Advantage: μ=0.497, σ=1.724, range=[-3.11, 4.10]

📊 Epoch 2 训练统计:
  平均奖励: 12.0334
  平均损失: 12.8952
  平均收益率: 0.8751
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 10/313, reward: 11.554, revenue_rate: 0.8772, efficiency: 0.8678, distance: 15.4929, memory: 0.1254, power: 0.4179
Test Batch 20/313, reward: 11.915, revenue_rate: 0.8788, efficiency: 0.8772, distance: 15.3597, memory: 0.1367, power: 0.4324
Test Batch 30/313, reward: 11.242, revenue_rate: 0.8794, efficiency: 0.8777, distance: 15.6419, memory: 0.1268, power: 0.4220
Test Batch 40/313, reward: 11.409, revenue_rate: 0.8750, efficiency: 0.8742, distance: 15.6937, memory: 0.1296, power: 0.4242
Test Batch 50/313, reward: 11.665, revenue_rate: 0.8814, efficiency: 0.8630, distance: 15.5315, memory: 0.1419, power: 0.4313
Test Batch 60/313, reward: 12.643, revenue_rate: 0.8670, efficiency: 0.8407, distance: 14.5481, memory: 0.1177, power: 0.4200
Test Batch 70/313, reward: 11.157, revenue_rate: 0.8628, efficiency: 0.8613, distance: 15.5179, memory: 0.1376, power: 0.4124
Test Batch 80/313, reward: 12.230, revenue_rate: 0.8788, efficiency: 0.8772, distance: 15.1695, memory: 0.1328, power: 0.4257
Test Batch 90/313, reward: 12.015, revenue_rate: 0.8867, efficiency: 0.8767, distance: 15.3659, memory: 0.1315, power: 0.4277
Test Batch 100/313, reward: 12.359, revenue_rate: 0.8763, efficiency: 0.8673, distance: 15.2029, memory: 0.1342, power: 0.4310
Test Batch 110/313, reward: 11.850, revenue_rate: 0.8902, efficiency: 0.8891, distance: 15.6539, memory: 0.1293, power: 0.4394
Test Batch 120/313, reward: 11.645, revenue_rate: 0.8656, efficiency: 0.8475, distance: 15.3099, memory: 0.1207, power: 0.4265
Test Batch 130/313, reward: 11.826, revenue_rate: 0.8776, efficiency: 0.8686, distance: 15.2549, memory: 0.1307, power: 0.4212
Test Batch 140/313, reward: 11.916, revenue_rate: 0.8708, efficiency: 0.8359, distance: 15.0559, memory: 0.1275, power: 0.4213
Test Batch 150/313, reward: 12.866, revenue_rate: 0.8758, efficiency: 0.8577, distance: 15.0055, memory: 0.1279, power: 0.4157
Test Batch 160/313, reward: 11.417, revenue_rate: 0.8800, efficiency: 0.8789, distance: 15.5926, memory: 0.1218, power: 0.4255
Test Batch 170/313, reward: 10.938, revenue_rate: 0.8742, efficiency: 0.8737, distance: 15.6505, memory: 0.1286, power: 0.4259
Test Batch 180/313, reward: 13.063, revenue_rate: 0.8724, efficiency: 0.8543, distance: 14.9965, memory: 0.1244, power: 0.4225
Test Batch 190/313, reward: 11.921, revenue_rate: 0.8855, efficiency: 0.8842, distance: 15.4499, memory: 0.1474, power: 0.4284
Test Batch 200/313, reward: 12.037, revenue_rate: 0.8705, efficiency: 0.8439, distance: 14.9980, memory: 0.1363, power: 0.4215
Test Batch 210/313, reward: 12.291, revenue_rate: 0.8849, efficiency: 0.8838, distance: 15.5448, memory: 0.1285, power: 0.4294
Test Batch 220/313, reward: 11.031, revenue_rate: 0.8771, efficiency: 0.8761, distance: 15.7187, memory: 0.1321, power: 0.4383
Test Batch 230/313, reward: 11.100, revenue_rate: 0.8693, efficiency: 0.8685, distance: 15.2281, memory: 0.1204, power: 0.4235
Test Batch 240/313, reward: 12.625, revenue_rate: 0.8759, efficiency: 0.8494, distance: 14.8898, memory: 0.1370, power: 0.4195
Test Batch 250/313, reward: 11.984, revenue_rate: 0.8657, efficiency: 0.8397, distance: 15.1630, memory: 0.1134, power: 0.4244
Test Batch 260/313, reward: 12.895, revenue_rate: 0.8737, efficiency: 0.8554, distance: 14.8885, memory: 0.1204, power: 0.4140
Test Batch 270/313, reward: 11.591, revenue_rate: 0.8687, efficiency: 0.8679, distance: 15.4801, memory: 0.1244, power: 0.4208
Test Batch 280/313, reward: 12.133, revenue_rate: 0.8724, efficiency: 0.8700, distance: 15.3344, memory: 0.1198, power: 0.4254
Test Batch 290/313, reward: 10.323, revenue_rate: 0.8615, efficiency: 0.8524, distance: 15.7606, memory: 0.1260, power: 0.4134
Test Batch 300/313, reward: 12.875, revenue_rate: 0.8810, efficiency: 0.8793, distance: 15.2184, memory: 0.1337, power: 0.4271
Test Batch 310/313, reward: 12.449, revenue_rate: 0.8762, efficiency: 0.8660, distance: 15.2339, memory: 0.1232, power: 0.4243
Test Batch 313/313, reward: 13.013, revenue_rate: 0.8772, efficiency: 0.8416, distance: 14.7097, memory: 0.1384, power: 0.4273
Test Summary - Avg reward: 12.013±3.189, revenue_rate: 0.8763±0.0341, efficiency: 0.8648, completion_rate: 0.9869, distance: 15.2889, memory: 0.1320, power: 0.4260
Load Balance - Avg balance score: -0.7302±0.0069
Task Distribution by Satellite:
  Satellite 1: 336 tasks (0.03%)
  Satellite 2: 349 tasks (0.04%)
  Satellite 3: 977251 tasks (99.93%)
✅ 验证完成 - Epoch 2, reward: 12.013, revenue_rate: 0.8763, distance: 15.2889, memory: 0.1320, power: 0.4260
  ✅ 训练验证差距正常: 0.0203
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_09_22_29_56 (验证集奖励: 12.0131)

开始训练 Epoch 3/3
Batch 0: Reward: 12.8911, Loss: 13.5939, Revenue: 0.8753, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:3(0.1%), S2:3163(99.8%)], ActorGrad: 29.6519, CriticGrad: 19.5205, Advantage: μ=0.633, σ=1.677, range=[-3.69, 3.76]
Epoch 3, Batch 50/3125, loss: 11.727↓, reward: 12.236↓, critic_reward: 11.933, revenue_rate: 0.8709, distance: 15.1002, memory: 0.1286, power: 0.4237, lr: 0.000050, took: 512.056s
Batch 50: Reward: 11.6853, Loss: 18.6481, Revenue: 0.8780, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:3(0.1%), S2:3160(99.7%)], ActorGrad: 36.5669, CriticGrad: 11.7893, Advantage: μ=-0.009, σ=1.796, range=[-3.87, 2.98]
Epoch 3, Batch 100/3125, loss: 12.744↓, reward: 12.292↑, critic_reward: 11.926, revenue_rate: 0.8684, distance: 14.9600, memory: 0.1274, power: 0.4218, lr: 0.000050, took: 508.567s
Batch 100: Reward: 13.0943, Loss: 17.4919, Revenue: 0.8705, LoadBalance: 0.0000, Tasks: [S0:10(0.3%), S1:7(0.2%), S2:3151(99.5%)], ActorGrad: 37.2583, CriticGrad: 15.6799, Advantage: μ=0.506, σ=1.721, range=[-2.39, 4.56]
Epoch 3, Batch 150/3125, loss: 12.720↓, reward: 12.520↓, critic_reward: 12.044, revenue_rate: 0.8583, distance: 14.8935, memory: 0.1221, power: 0.4159, lr: 0.000050, took: 517.698s
Batch 150: Reward: 12.1792, Loss: 11.2482, Revenue: 0.8687, LoadBalance: 0.0000, Tasks: [S0:11(0.3%), S1:18(0.6%), S2:3139(99.1%)], ActorGrad: 40.8436, CriticGrad: 8.3593, Advantage: μ=-0.126, σ=1.791, range=[-4.17, 3.65]
Epoch 3, Batch 200/3125, loss: 13.429↑, reward: 12.587↑, critic_reward: 12.137, revenue_rate: 0.8486, distance: 14.8363, memory: 0.1184, power: 0.4114, lr: 0.000050, took: 516.638s
Batch 200: Reward: 11.9770, Loss: 16.7256, Revenue: 0.8543, LoadBalance: 0.0000, Tasks: [S0:19(0.6%), S1:18(0.6%), S2:3131(98.8%)], ActorGrad: 35.1395, CriticGrad: 11.1870, Advantage: μ=-0.021, σ=1.796, range=[-3.87, 3.71]
Epoch 3, Batch 250/3125, loss: 13.013↓, reward: 12.310↑, critic_reward: 12.059, revenue_rate: 0.8570, distance: 15.0071, memory: 0.1225, power: 0.4143, lr: 0.000050, took: 517.412s
Batch 250: Reward: 12.4799, Loss: 15.6973, Revenue: 0.8803, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:1(0.0%), S2:3166(99.9%)], ActorGrad: 40.3806, CriticGrad: 15.5908, Advantage: μ=0.387, σ=1.752, range=[-2.80, 4.22]
Epoch 3, Batch 300/3125, loss: 12.685↓, reward: 12.801↑, critic_reward: 11.984, revenue_rate: 0.8565, distance: 14.6668, memory: 0.1210, power: 0.4152, lr: 0.000050, took: 518.256s
Batch 300: Reward: 12.6460, Loss: 11.5477, Revenue: 0.8252, LoadBalance: 0.0000, Tasks: [S0:74(2.3%), S1:22(0.7%), S2:3072(97.0%)], ActorGrad: 74.1135, CriticGrad: 6.6881, Advantage: μ=0.117, σ=1.792, range=[-3.68, 3.73]
Epoch 3, Batch 350/3125, loss: 13.613↓, reward: 13.207↑, critic_reward: 12.197, revenue_rate: 0.8471, distance: 14.3508, memory: 0.1183, power: 0.4117, lr: 0.000050, took: 521.825s
Batch 350: Reward: 13.2163, Loss: 12.1906, Revenue: 0.8592, LoadBalance: 0.0000, Tasks: [S0:28(0.9%), S1:13(0.4%), S2:3127(98.7%)], ActorGrad: 46.4713, CriticGrad: 17.5286, Advantage: μ=0.578, σ=1.697, range=[-1.94, 3.39]
Epoch 3, Batch 400/3125, loss: 13.700↑, reward: 13.363↓, critic_reward: 12.327, revenue_rate: 0.8561, distance: 14.4148, memory: 0.1223, power: 0.4145, lr: 0.000050, took: 520.621s
Batch 400: Reward: 13.5900, Loss: 13.2026, Revenue: 0.8746, LoadBalance: 0.0000, Tasks: [S0:18(0.6%), S1:10(0.3%), S2:3140(99.1%)], ActorGrad: 52.1130, CriticGrad: 19.8611, Advantage: μ=0.651, σ=1.670, range=[-1.80, 4.97]
Epoch 3, Batch 450/3125, loss: 13.300↓, reward: 13.593↑, critic_reward: 12.412, revenue_rate: 0.8481, distance: 14.3045, memory: 0.1187, power: 0.4113, lr: 0.000050, took: 523.370s
Batch 450: Reward: 14.4401, Loss: 19.4935, Revenue: 0.8382, LoadBalance: 0.0000, Tasks: [S0:81(2.6%), S1:63(2.0%), S2:3024(95.5%)], ActorGrad: 93.8263, CriticGrad: 31.2035, Advantage: μ=0.815, σ=1.594, range=[-3.57, 3.87]
Epoch 3, Batch 500/3125, loss: 13.750↓, reward: 13.541↓, critic_reward: 12.476, revenue_rate: 0.8441, distance: 14.4569, memory: 0.1205, power: 0.4096, lr: 0.000050, took: 537.723s
Batch 500: Reward: 13.2763, Loss: 13.5312, Revenue: 0.8459, LoadBalance: 0.0000, Tasks: [S0:103(3.3%), S1:78(2.5%), S2:2987(94.3%)], ActorGrad: 97.6359, CriticGrad: 19.0316, Advantage: μ=0.555, σ=1.705, range=[-2.39, 3.86]
Epoch 3, Batch 550/3125, loss: 13.872↑, reward: 13.337↓, critic_reward: 12.581, revenue_rate: 0.8469, distance: 14.6106, memory: 0.1210, power: 0.4098, lr: 0.000050, took: 525.718s
Batch 550: Reward: 12.6717, Loss: 16.6600, Revenue: 0.7945, LoadBalance: 0.0000, Tasks: [S0:261(8.4%), S1:116(3.7%), S2:2727(87.9%)], ActorGrad: 56.8669, CriticGrad: 12.4665, Advantage: μ=-0.210, σ=1.783, range=[-3.25, 3.40]
Epoch 3, Batch 600/3125, loss: 13.673↓, reward: 13.057↓, critic_reward: 12.585, revenue_rate: 0.8350, distance: 14.4449, memory: 0.1185, power: 0.4034, lr: 0.000050, took: 530.506s
Batch 600: Reward: 11.3343, Loss: 9.7448, Revenue: 0.7692, LoadBalance: 0.0000, Tasks: [S0:388(12.8%), S1:75(2.5%), S2:2577(84.8%)], ActorGrad: 79.8869, CriticGrad: 23.0884, Advantage: μ=-0.819, σ=1.592, range=[-4.05, 2.65]
Epoch 3, Batch 650/3125, loss: 13.744↑, reward: 12.721↑, critic_reward: 12.668, revenue_rate: 0.8154, distance: 14.3115, memory: 0.1087, power: 0.3952, lr: 0.000050, took: 534.692s
Batch 650: Reward: 12.4637, Loss: 20.0441, Revenue: 0.8622, LoadBalance: 0.0000, Tasks: [S0:55(1.7%), S1:7(0.2%), S2:3106(98.0%)], ActorGrad: 32.0810, CriticGrad: 11.5083, Advantage: μ=-0.173, σ=1.787, range=[-7.32, 2.95]
Epoch 3, Batch 700/3125, loss: 13.411↓, reward: 13.669↑, critic_reward: 12.672, revenue_rate: 0.8532, distance: 14.4801, memory: 0.1194, power: 0.4151, lr: 0.000050, took: 535.889s
Batch 700: Reward: 12.5617, Loss: 12.6917, Revenue: 0.8303, LoadBalance: 0.0000, Tasks: [S0:96(3.0%), S1:25(0.8%), S2:3047(96.2%)], ActorGrad: 46.0184, CriticGrad: 6.4541, Advantage: μ=-0.086, σ=1.794, range=[-3.59, 3.65]
Epoch 3, Batch 750/3125, loss: 13.326↑, reward: 13.785↑, critic_reward: 12.692, revenue_rate: 0.8584, distance: 14.6008, memory: 0.1231, power: 0.4172, lr: 0.000050, took: 532.673s
Batch 750: Reward: 13.6155, Loss: 7.0278, Revenue: 0.8506, LoadBalance: 0.0000, Tasks: [S0:59(1.9%), S1:43(1.4%), S2:3066(96.8%)], ActorGrad: 48.1871, CriticGrad: 6.1491, Advantage: μ=0.256, σ=1.777, range=[-2.92, 3.68]
Epoch 3, Batch 800/3125, loss: 13.561↑, reward: 13.472↑, critic_reward: 12.739, revenue_rate: 0.8629, distance: 14.8066, memory: 0.1290, power: 0.4182, lr: 0.000050, took: 525.405s
Batch 800: Reward: 13.5571, Loss: 11.8296, Revenue: 0.8519, LoadBalance: 0.0000, Tasks: [S0:82(2.6%), S1:56(1.8%), S2:3030(95.6%)], ActorGrad: 48.1469, CriticGrad: 11.7124, Advantage: μ=0.441, σ=1.739, range=[-3.42, 4.53]
Epoch 3, Batch 850/3125, loss: 13.322↑, reward: 13.839↑, critic_reward: 12.841, revenue_rate: 0.8540, distance: 14.6198, memory: 0.1230, power: 0.4135, lr: 0.000050, took: 528.863s
Batch 850: Reward: 14.0051, Loss: 10.7290, Revenue: 0.8407, LoadBalance: 0.0000, Tasks: [S0:53(1.7%), S1:55(1.7%), S2:3060(96.6%)], ActorGrad: 75.5399, CriticGrad: 11.1658, Advantage: μ=0.466, σ=1.732, range=[-3.08, 4.15]
Epoch 3, Batch 900/3125, loss: 12.849↑, reward: 13.791↑, critic_reward: 12.936, revenue_rate: 0.8544, distance: 14.5195, memory: 0.1219, power: 0.4156, lr: 0.000050, took: 528.596s
Batch 900: Reward: 13.4276, Loss: 11.3139, Revenue: 0.8438, LoadBalance: 0.0000, Tasks: [S0:58(1.8%), S1:42(1.3%), S2:3068(96.8%)], ActorGrad: 65.9899, CriticGrad: 17.0112, Advantage: μ=0.558, σ=1.704, range=[-3.33, 2.81]
Epoch 3, Batch 950/3125, loss: 13.656↓, reward: 13.478↑, critic_reward: 12.966, revenue_rate: 0.8302, distance: 14.3960, memory: 0.1144, power: 0.4014, lr: 0.000050, took: 536.093s
Batch 950: Reward: 14.1990, Loss: 9.8327, Revenue: 0.8293, LoadBalance: 0.0000, Tasks: [S0:220(6.9%), S1:47(1.5%), S2:2901(91.6%)], ActorGrad: 79.8622, CriticGrad: 16.9165, Advantage: μ=0.586, σ=1.695, range=[-2.56, 3.69]
Epoch 3, Batch 1000/3125, loss: 12.494↑, reward: 13.722↓, critic_reward: 12.924, revenue_rate: 0.8418, distance: 14.2731, memory: 0.1188, power: 0.4073, lr: 0.000050, took: 539.709s
Batch 1000: Reward: 14.9351, Loss: 13.0357, Revenue: 0.8481, LoadBalance: 0.0000, Tasks: [S0:115(3.6%), S1:21(0.7%), S2:3032(95.7%)], ActorGrad: 98.9149, CriticGrad: 28.4914, Advantage: μ=0.923, σ=1.532, range=[-2.46, 3.02]
Epoch 3, Batch 1050/3125, loss: 12.178↓, reward: 13.589↓, critic_reward: 13.082, revenue_rate: 0.8339, distance: 14.1803, memory: 0.1158, power: 0.4046, lr: 0.000050, took: 534.147s
Batch 1050: Reward: 13.4341, Loss: 9.0140, Revenue: 0.8295, LoadBalance: 0.0000, Tasks: [S0:172(5.4%), S1:44(1.4%), S2:2952(93.2%)], ActorGrad: 51.0902, CriticGrad: 4.5944, Advantage: μ=0.014, σ=1.796, range=[-5.22, 3.39]
Epoch 3, Batch 1100/3125, loss: 13.093↑, reward: 13.996↑, critic_reward: 13.138, revenue_rate: 0.8475, distance: 14.1780, memory: 0.1161, power: 0.4097, lr: 0.000050, took: 532.117s
Batch 1100: Reward: 14.0955, Loss: 10.1806, Revenue: 0.8733, LoadBalance: 0.0000, Tasks: [S0:29(0.9%), S1:25(0.8%), S2:3114(98.3%)], ActorGrad: 46.3757, CriticGrad: 7.1213, Advantage: μ=0.264, σ=1.776, range=[-2.88, 4.74]
Epoch 3, Batch 1150/3125, loss: 12.278↓, reward: 13.801↑, critic_reward: 13.263, revenue_rate: 0.8552, distance: 14.3119, memory: 0.1197, power: 0.4143, lr: 0.000050, took: 523.217s
Batch 1150: Reward: 14.5754, Loss: 13.7336, Revenue: 0.8440, LoadBalance: 0.0000, Tasks: [S0:61(1.9%), S1:56(1.8%), S2:3051(96.3%)], ActorGrad: 57.0880, CriticGrad: 21.6345, Advantage: μ=0.737, σ=1.633, range=[-3.49, 3.78]
Epoch 3, Batch 1200/3125, loss: 12.522↓, reward: 13.929↓, critic_reward: 13.207, revenue_rate: 0.8511, distance: 14.2518, memory: 0.1195, power: 0.4119, lr: 0.000050, took: 524.377s
Batch 1200: Reward: 14.9228, Loss: 17.7785, Revenue: 0.8358, LoadBalance: 0.0000, Tasks: [S0:114(3.6%), S1:86(2.7%), S2:2968(93.7%)], ActorGrad: 46.5201, CriticGrad: 26.7468, Advantage: μ=0.731, σ=1.635, range=[-1.62, 4.34]
Epoch 3, Batch 1250/3125, loss: 12.718↓, reward: 13.348↓, critic_reward: 13.275, revenue_rate: 0.8099, distance: 13.9489, memory: 0.1121, power: 0.3911, lr: 0.000050, took: 538.494s
Batch 1250: Reward: 13.4576, Loss: 7.6734, Revenue: 0.8040, LoadBalance: 0.0000, Tasks: [S0:275(8.7%), S1:101(3.2%), S2:2792(88.1%)], ActorGrad: 58.4028, CriticGrad: 10.6476, Advantage: μ=0.431, σ=1.742, range=[-3.76, 3.39]
Epoch 3, Batch 1300/3125, loss: 12.131↑, reward: 13.587↑, critic_reward: 13.176, revenue_rate: 0.8261, distance: 14.0047, memory: 0.1133, power: 0.3996, lr: 0.000050, took: 530.421s
Batch 1300: Reward: 13.6174, Loss: 11.3911, Revenue: 0.8438, LoadBalance: 0.0000, Tasks: [S0:64(2.0%), S1:32(1.0%), S2:3072(97.0%)], ActorGrad: 51.0026, CriticGrad: 6.9981, Advantage: μ=0.120, σ=1.792, range=[-3.62, 4.31]
Epoch 3, Batch 1350/3125, loss: 12.682↑, reward: 13.811↑, critic_reward: 13.289, revenue_rate: 0.8210, distance: 13.9063, memory: 0.1077, power: 0.3970, lr: 0.000050, took: 536.171s
Batch 1350: Reward: 14.7520, Loss: 12.2613, Revenue: 0.8485, LoadBalance: 0.0000, Tasks: [S0:59(1.9%), S1:28(0.9%), S2:3081(97.3%)], ActorGrad: 39.7474, CriticGrad: 26.7020, Advantage: μ=0.918, σ=1.535, range=[-2.57, 4.49]
Epoch 3, Batch 1400/3125, loss: 11.711↓, reward: 14.046↓, critic_reward: 13.332, revenue_rate: 0.8431, distance: 14.0074, memory: 0.1143, power: 0.4077, lr: 0.000050, took: 528.502s
Batch 1400: Reward: 13.4810, Loss: 11.5721, Revenue: 0.8137, LoadBalance: 0.0000, Tasks: [S0:146(4.6%), S1:60(1.9%), S2:2962(93.5%)], ActorGrad: 57.5725, CriticGrad: 9.1990, Advantage: μ=0.169, σ=1.788, range=[-3.42, 4.19]
Epoch 3, Batch 1450/3125, loss: 11.841↓, reward: 13.796↑, critic_reward: 13.413, revenue_rate: 0.8196, distance: 13.8686, memory: 0.1106, power: 0.3968, lr: 0.000050, took: 536.257s
Batch 1450: Reward: 14.1382, Loss: 13.7120, Revenue: 0.8191, LoadBalance: 0.0000, Tasks: [S0:208(6.6%), S1:69(2.2%), S2:2891(91.3%)], ActorGrad: 51.0969, CriticGrad: 8.6558, Advantage: μ=0.223, σ=1.782, range=[-3.38, 4.55]
Epoch 3, Batch 1500/3125, loss: 14.308↑, reward: 12.959↓, critic_reward: 13.289, revenue_rate: 0.7907, distance: 13.8754, memory: 0.1018, power: 0.3823, lr: 0.000050, took: 539.423s
Batch 1500: Reward: 13.5075, Loss: 11.0704, Revenue: 0.7922, LoadBalance: 0.0000, Tasks: [S0:514(16.2%), S1:74(2.3%), S2:2580(81.4%)], ActorGrad: 91.4656, CriticGrad: 11.8678, Advantage: μ=0.406, σ=1.748, range=[-4.22, 3.93]
Epoch 3, Batch 1550/3125, loss: 11.623↑, reward: 13.552↑, critic_reward: 13.184, revenue_rate: 0.8091, distance: 13.8286, memory: 0.1035, power: 0.3917, lr: 0.000050, took: 540.318s
Batch 1550: Reward: 13.7443, Loss: 12.1464, Revenue: 0.8264, LoadBalance: 0.0000, Tasks: [S0:156(4.9%), S1:35(1.1%), S2:2977(94.0%)], ActorGrad: 70.0037, CriticGrad: 5.4392, Advantage: μ=0.120, σ=1.792, range=[-2.98, 3.59]
Epoch 3, Batch 1600/3125, loss: 12.485↑, reward: 14.136↑, critic_reward: 13.343, revenue_rate: 0.8422, distance: 13.8883, memory: 0.1143, power: 0.4065, lr: 0.000050, took: 530.306s
Batch 1600: Reward: 14.8108, Loss: 13.8283, Revenue: 0.8557, LoadBalance: 0.0000, Tasks: [S0:54(1.7%), S1:22(0.7%), S2:3092(97.6%)], ActorGrad: 62.1022, CriticGrad: 25.2166, Advantage: μ=0.807, σ=1.598, range=[-4.11, 4.94]
Epoch 3, Batch 1650/3125, loss: 12.574↑, reward: 13.989↓, critic_reward: 13.373, revenue_rate: 0.8468, distance: 14.1474, memory: 0.1188, power: 0.4106, lr: 0.000050, took: 525.828s
Batch 1650: Reward: 13.9594, Loss: 11.1991, Revenue: 0.8531, LoadBalance: 0.0000, Tasks: [S0:40(1.3%), S1:17(0.5%), S2:3111(98.2%)], ActorGrad: 39.0764, CriticGrad: 6.6239, Advantage: μ=0.184, σ=1.786, range=[-2.45, 3.71]
Epoch 3, Batch 1700/3125, loss: 13.284↓, reward: 14.291↓, critic_reward: 13.515, revenue_rate: 0.8392, distance: 14.0545, memory: 0.1145, power: 0.4056, lr: 0.000050, took: 530.951s
Batch 1700: Reward: 13.9660, Loss: 17.3378, Revenue: 0.8154, LoadBalance: 0.0000, Tasks: [S0:254(8.0%), S1:112(3.5%), S2:2802(88.4%)], ActorGrad: 55.1471, CriticGrad: 9.8751, Advantage: μ=0.205, σ=1.784, range=[-4.81, 3.08]
Epoch 3, Batch 1750/3125, loss: 12.588↓, reward: 14.152↓, critic_reward: 13.599, revenue_rate: 0.8254, distance: 13.9060, memory: 0.1106, power: 0.4003, lr: 0.000050, took: 535.592s
Batch 1750: Reward: 14.0524, Loss: 14.3460, Revenue: 0.8381, LoadBalance: 0.0000, Tasks: [S0:107(3.4%), S1:65(2.1%), S2:2996(94.6%)], ActorGrad: 48.0784, CriticGrad: 8.6510, Advantage: μ=0.283, σ=1.773, range=[-3.44, 3.58]
Epoch 3, Batch 1800/3125, loss: 13.046↓, reward: 14.303↑, critic_reward: 13.551, revenue_rate: 0.8385, distance: 13.9923, memory: 0.1140, power: 0.4056, lr: 0.000050, took: 533.827s
Batch 1800: Reward: 14.2019, Loss: 10.3392, Revenue: 0.8452, LoadBalance: 0.0000, Tasks: [S0:107(3.4%), S1:54(1.7%), S2:3007(94.9%)], ActorGrad: 53.8217, CriticGrad: 6.2111, Advantage: μ=0.211, σ=1.783, range=[-3.28, 3.38]
Epoch 3, Batch 1850/3125, loss: 12.188↑, reward: 14.351↓, critic_reward: 13.722, revenue_rate: 0.8412, distance: 14.0688, memory: 0.1157, power: 0.4089, lr: 0.000050, took: 534.672s
Batch 1850: Reward: 14.7215, Loss: 17.7819, Revenue: 0.8288, LoadBalance: 0.0000, Tasks: [S0:191(6.0%), S1:73(2.3%), S2:2904(91.7%)], ActorGrad: 63.7631, CriticGrad: 13.8608, Advantage: μ=0.375, σ=1.755, range=[-3.83, 4.34]
Epoch 3, Batch 1900/3125, loss: 12.209↓, reward: 14.476↓, critic_reward: 13.817, revenue_rate: 0.8367, distance: 13.9099, memory: 0.1131, power: 0.4058, lr: 0.000050, took: 532.653s
Batch 1900: Reward: 13.9611, Loss: 10.1505, Revenue: 0.8537, LoadBalance: 0.0000, Tasks: [S0:119(3.8%), S1:81(2.6%), S2:2968(93.7%)], ActorGrad: 55.7841, CriticGrad: 7.7023, Advantage: μ=0.082, σ=1.794, range=[-6.18, 3.69]
Epoch 3, Batch 1950/3125, loss: 12.281↑, reward: 14.331↑, critic_reward: 13.766, revenue_rate: 0.8566, distance: 14.3646, memory: 0.1213, power: 0.4151, lr: 0.000050, took: 529.050s
Batch 1950: Reward: 14.0662, Loss: 14.0472, Revenue: 0.8653, LoadBalance: 0.0000, Tasks: [S0:36(1.1%), S1:46(1.5%), S2:3086(97.4%)], ActorGrad: 66.7819, CriticGrad: 7.2449, Advantage: μ=0.121, σ=1.792, range=[-5.66, 2.92]
Epoch 3, Batch 2000/3125, loss: 12.748↓, reward: 14.153↓, critic_reward: 13.745, revenue_rate: 0.8623, distance: 14.5163, memory: 0.1253, power: 0.4189, lr: 0.000050, took: 529.011s
Batch 2000: Reward: 15.0218, Loss: 14.2654, Revenue: 0.8621, LoadBalance: 0.0000, Tasks: [S0:71(2.2%), S1:77(2.4%), S2:3020(95.3%)], ActorGrad: 51.3320, CriticGrad: 18.8127, Advantage: μ=0.657, σ=1.667, range=[-1.68, 5.68]
Epoch 3, Batch 2050/3125, loss: 13.566↓, reward: 14.351↓, critic_reward: 13.743, revenue_rate: 0.8393, distance: 14.2264, memory: 0.1171, power: 0.4089, lr: 0.000050, took: 535.015s
Batch 2050: Reward: 14.7573, Loss: 11.3312, Revenue: 0.8309, LoadBalance: 0.0000, Tasks: [S0:411(13.0%), S1:134(4.2%), S2:2623(82.8%)], ActorGrad: 51.1406, CriticGrad: 11.3725, Advantage: μ=0.307, σ=1.769, range=[-3.76, 3.90]
Epoch 3, Batch 2100/3125, loss: 13.152↑, reward: 14.371↓, critic_reward: 13.891, revenue_rate: 0.8185, distance: 14.0090, memory: 0.1049, power: 0.3955, lr: 0.000050, took: 536.965s
Batch 2100: Reward: 14.4269, Loss: 15.5953, Revenue: 0.8223, LoadBalance: 0.0071, Tasks: [S0:732(23.1%), S1:108(3.4%), S2:2328(73.5%)], ActorGrad: 180.7824, CriticGrad: 11.7459, Advantage: μ=0.305, σ=1.769, range=[-3.33, 2.93]
Epoch 3, Batch 2150/3125, loss: 12.954↓, reward: 14.739↓, critic_reward: 13.968, revenue_rate: 0.8287, distance: 13.9625, memory: 0.1089, power: 0.4022, lr: 0.000050, took: 531.538s
Batch 2150: Reward: 15.5366, Loss: 19.3683, Revenue: 0.8543, LoadBalance: 0.0000, Tasks: [S0:356(11.2%), S1:62(2.0%), S2:2750(86.8%)], ActorGrad: 61.3588, CriticGrad: 19.5782, Advantage: μ=0.548, σ=1.707, range=[-3.56, 3.61]
Epoch 3, Batch 2200/3125, loss: 12.726↓, reward: 14.697↓, critic_reward: 13.970, revenue_rate: 0.8510, distance: 14.2952, memory: 0.1195, power: 0.4142, lr: 0.000050, took: 536.319s
Batch 2200: Reward: 15.4490, Loss: 20.3583, Revenue: 0.8508, LoadBalance: 0.0000, Tasks: [S0:219(6.9%), S1:88(2.8%), S2:2861(90.3%)], ActorGrad: 54.7330, CriticGrad: 23.8516, Advantage: μ=0.652, σ=1.669, range=[-2.74, 4.16]
Epoch 3, Batch 2250/3125, loss: 13.544↓, reward: 14.973↓, critic_reward: 14.005, revenue_rate: 0.8426, distance: 14.0785, memory: 0.1179, power: 0.4097, lr: 0.000050, took: 538.624s
Batch 2250: Reward: 14.1839, Loss: 7.9315, Revenue: 0.8383, LoadBalance: 0.0000, Tasks: [S0:273(8.6%), S1:66(2.1%), S2:2829(89.3%)], ActorGrad: 60.7036, CriticGrad: 7.3456, Advantage: μ=0.197, σ=1.785, range=[-2.87, 5.05]
Epoch 3, Batch 2300/3125, loss: 12.271↑, reward: 14.873↑, critic_reward: 14.097, revenue_rate: 0.8438, distance: 14.0260, memory: 0.1186, power: 0.4099, lr: 0.000050, took: 538.408s
Batch 2300: Reward: 14.1091, Loss: 9.8301, Revenue: 0.8485, LoadBalance: 0.0000, Tasks: [S0:154(4.9%), S1:53(1.7%), S2:2961(93.5%)], ActorGrad: 64.6446, CriticGrad: 5.0991, Advantage: μ=-0.039, σ=1.796, range=[-3.74, 2.92]
Epoch 3, Batch 2350/3125, loss: 12.734↑, reward: 14.913↑, critic_reward: 14.100, revenue_rate: 0.8398, distance: 13.9841, memory: 0.1182, power: 0.4081, lr: 0.000050, took: 536.326s
Batch 2350: Reward: 14.1283, Loss: 10.0826, Revenue: 0.8295, LoadBalance: 0.0008, Tasks: [S0:508(16.0%), S1:125(3.9%), S2:2535(80.0%)], ActorGrad: 63.0606, CriticGrad: 7.8489, Advantage: μ=-0.059, σ=1.795, range=[-3.64, 3.71]
Epoch 3, Batch 2400/3125, loss: 12.312↑, reward: 14.779↑, critic_reward: 14.246, revenue_rate: 0.8223, distance: 13.8402, memory: 0.1087, power: 0.3986, lr: 0.000050, took: 531.457s
Batch 2400: Reward: 13.6350, Loss: 9.4345, Revenue: 0.7962, LoadBalance: 0.0440, Tasks: [S0:795(25.1%), S1:154(4.9%), S2:2219(70.0%)], ActorGrad: 136.3211, CriticGrad: 17.4668, Advantage: μ=-0.535, σ=1.712, range=[-4.20, 2.68]
Epoch 3, Batch 2450/3125, loss: 12.740↑, reward: 14.194↑, critic_reward: 14.253, revenue_rate: 0.7987, distance: 13.8401, memory: 0.0971, power: 0.3861, lr: 0.000050, took: 530.107s
Batch 2450: Reward: 15.3308, Loss: 9.7263, Revenue: 0.8129, LoadBalance: 0.1492, Tasks: [S0:1211(38.2%), S1:101(3.2%), S2:1856(58.6%)], ActorGrad: 115.0052, CriticGrad: 14.0616, Advantage: μ=0.465, σ=1.733, range=[-3.89, 3.99]
Epoch 3, Batch 2500/3125, loss: 12.462↓, reward: 14.781↓, critic_reward: 14.322, revenue_rate: 0.8120, distance: 13.7967, memory: 0.1021, power: 0.3927, lr: 0.000050, took: 529.008s
Batch 2500: Reward: 15.1991, Loss: 12.1662, Revenue: 0.8265, LoadBalance: 0.0297, Tasks: [S0:914(28.9%), S1:81(2.6%), S2:2173(68.6%)], ActorGrad: 63.0163, CriticGrad: 16.5806, Advantage: μ=0.632, σ=1.678, range=[-3.26, 4.84]
Epoch 3, Batch 2550/3125, loss: 13.295↑, reward: 15.145↓, critic_reward: 14.250, revenue_rate: 0.8246, distance: 13.7917, memory: 0.1085, power: 0.3995, lr: 0.000050, took: 528.764s
Batch 2550: Reward: 15.4676, Loss: 12.0430, Revenue: 0.8274, LoadBalance: 0.0061, Tasks: [S0:761(24.0%), S1:78(2.5%), S2:2329(73.5%)], ActorGrad: 108.9069, CriticGrad: 12.6648, Advantage: μ=0.483, σ=1.728, range=[-2.75, 4.28]
Epoch 3, Batch 2600/3125, loss: 13.152↑, reward: 15.153↑, critic_reward: 14.361, revenue_rate: 0.8283, distance: 13.7831, memory: 0.1080, power: 0.3996, lr: 0.000050, took: 530.808s
Batch 2600: Reward: 15.1833, Loss: 11.5992, Revenue: 0.8261, LoadBalance: 0.0008, Tasks: [S0:708(22.3%), S1:59(1.9%), S2:2401(75.8%)], ActorGrad: 51.6937, CriticGrad: 20.2042, Advantage: μ=0.711, σ=1.644, range=[-3.33, 3.25]
Epoch 3, Batch 2650/3125, loss: 13.071↑, reward: 15.112↓, critic_reward: 14.393, revenue_rate: 0.8309, distance: 13.8389, memory: 0.1112, power: 0.4029, lr: 0.000050, took: 530.648s
Batch 2650: Reward: 15.3142, Loss: 13.6773, Revenue: 0.8287, LoadBalance: 0.0181, Tasks: [S0:851(26.9%), S1:89(2.8%), S2:2228(70.3%)], ActorGrad: 76.3797, CriticGrad: 13.8807, Advantage: μ=0.442, σ=1.739, range=[-3.28, 3.42]
Epoch 3, Batch 2700/3125, loss: 13.600↓, reward: 15.359↓, critic_reward: 14.464, revenue_rate: 0.8252, distance: 13.7696, memory: 0.1095, power: 0.3996, lr: 0.000050, took: 529.560s
Batch 2700: Reward: 15.1565, Loss: 13.3736, Revenue: 0.8111, LoadBalance: 0.0390, Tasks: [S0:869(27.4%), S1:107(3.4%), S2:2192(69.2%)], ActorGrad: 75.6663, CriticGrad: 19.2752, Advantage: μ=0.569, σ=1.700, range=[-3.21, 4.75]
Epoch 3, Batch 2750/3125, loss: 12.230↑, reward: 15.097↓, critic_reward: 14.621, revenue_rate: 0.8309, distance: 13.8936, memory: 0.1142, power: 0.4034, lr: 0.000050, took: 532.846s
Batch 2750: Reward: 15.1816, Loss: 11.1507, Revenue: 0.8295, LoadBalance: 0.0000, Tasks: [S0:444(14.0%), S1:132(4.2%), S2:2592(81.8%)], ActorGrad: 54.1986, CriticGrad: 4.7527, Advantage: μ=0.114, σ=1.792, range=[-2.84, 3.43]
Epoch 3, Batch 2800/3125, loss: 12.532↑, reward: 15.238↓, critic_reward: 14.553, revenue_rate: 0.8391, distance: 13.9496, memory: 0.1217, power: 0.4083, lr: 0.000050, took: 534.533s
Batch 2800: Reward: 14.7855, Loss: 6.5262, Revenue: 0.8361, LoadBalance: 0.0000, Tasks: [S0:298(9.4%), S1:143(4.5%), S2:2727(86.1%)], ActorGrad: 127.7360, CriticGrad: 6.3080, Advantage: μ=0.071, σ=1.795, range=[-3.25, 3.62]
Epoch 3, Batch 2850/3125, loss: 12.898↑, reward: 15.035↑, critic_reward: 14.651, revenue_rate: 0.8416, distance: 14.0740, memory: 0.1215, power: 0.4093, lr: 0.000050, took: 533.636s
Batch 2850: Reward: 15.7494, Loss: 10.9792, Revenue: 0.8422, LoadBalance: 0.0000, Tasks: [S0:395(12.5%), S1:153(4.8%), S2:2620(82.7%)], ActorGrad: 56.9571, CriticGrad: 9.7112, Advantage: μ=0.344, σ=1.762, range=[-3.72, 4.01]
Epoch 3, Batch 2900/3125, loss: 12.758↑, reward: 15.198↑, critic_reward: 14.712, revenue_rate: 0.8327, distance: 13.9456, memory: 0.1120, power: 0.4042, lr: 0.000050, took: 533.872s
Batch 2900: Reward: 15.3745, Loss: 13.2825, Revenue: 0.8326, LoadBalance: 0.0012, Tasks: [S0:509(16.1%), S1:209(6.6%), S2:2450(77.3%)], ActorGrad: 62.7299, CriticGrad: 11.0432, Advantage: μ=0.127, σ=1.791, range=[-3.27, 5.59]
Epoch 3, Batch 2950/3125, loss: 12.695↓, reward: 14.822↓, critic_reward: 14.616, revenue_rate: 0.8495, distance: 14.3417, memory: 0.1235, power: 0.4156, lr: 0.000050, took: 531.486s
Batch 2950: Reward: 14.8248, Loss: 11.5628, Revenue: 0.8662, LoadBalance: 0.0000, Tasks: [S0:36(1.1%), S1:53(1.7%), S2:3079(97.2%)], ActorGrad: 29.0742, CriticGrad: 12.4478, Advantage: μ=-0.004, σ=1.796, range=[-2.94, 5.47]
Epoch 3, Batch 3000/3125, loss: 13.287↑, reward: 14.580↓, critic_reward: 14.553, revenue_rate: 0.8568, distance: 14.5835, memory: 0.1238, power: 0.4183, lr: 0.000050, took: 528.366s
Batch 3000: Reward: 14.3974, Loss: 19.8105, Revenue: 0.8326, LoadBalance: 0.0000, Tasks: [S0:224(7.1%), S1:172(5.4%), S2:2772(87.5%)], ActorGrad: 58.3383, CriticGrad: 10.7889, Advantage: μ=0.092, σ=1.794, range=[-4.47, 2.58]
Epoch 3, Batch 3050/3125, loss: 13.184↓, reward: 15.320↑, critic_reward: 14.629, revenue_rate: 0.8436, distance: 14.0507, memory: 0.1232, power: 0.4118, lr: 0.000050, took: 534.875s
Batch 3050: Reward: 14.5245, Loss: 9.2697, Revenue: 0.8502, LoadBalance: 0.0000, Tasks: [S0:194(6.1%), S1:135(4.3%), S2:2839(89.6%)], ActorGrad: 72.6294, CriticGrad: 13.1532, Advantage: μ=-0.287, σ=1.772, range=[-4.74, 3.68]
Epoch 3, Batch 3100/3125, loss: 12.989↓, reward: 14.983↑, critic_reward: 14.670, revenue_rate: 0.8476, distance: 14.1990, memory: 0.1254, power: 0.4146, lr: 0.000050, took: 531.755s
Batch 3100: Reward: 14.4298, Loss: 10.9303, Revenue: 0.8432, LoadBalance: 0.0000, Tasks: [S0:193(6.1%), S1:117(3.7%), S2:2858(90.2%)], ActorGrad: 50.0037, CriticGrad: 11.1987, Advantage: μ=-0.156, σ=1.789, range=[-3.85, 3.01]

📊 Epoch 3 训练统计:
  平均奖励: 14.0350
  平均损失: 12.8934
  平均收益率: 0.8396
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 10/313, reward: 15.596, revenue_rate: 0.8487, efficiency: 0.8390, distance: 13.8677, memory: 0.1133, power: 0.4052
Test Batch 20/313, reward: 15.590, revenue_rate: 0.8447, efficiency: 0.8372, distance: 13.6930, memory: 0.1329, power: 0.4209
Test Batch 30/313, reward: 15.456, revenue_rate: 0.8504, efficiency: 0.8430, distance: 13.8900, memory: 0.1116, power: 0.4051
Test Batch 40/313, reward: 15.413, revenue_rate: 0.8482, efficiency: 0.8392, distance: 14.0180, memory: 0.1214, power: 0.4117
Test Batch 50/313, reward: 15.833, revenue_rate: 0.8544, efficiency: 0.8477, distance: 13.6870, memory: 0.1254, power: 0.4151
Test Batch 60/313, reward: 15.656, revenue_rate: 0.8508, efficiency: 0.8449, distance: 13.8256, memory: 0.1211, power: 0.4116
Test Batch 70/313, reward: 15.213, revenue_rate: 0.8423, efficiency: 0.8347, distance: 13.9910, memory: 0.1277, power: 0.3986
Test Batch 80/313, reward: 15.683, revenue_rate: 0.8555, efficiency: 0.8485, distance: 14.0356, memory: 0.1196, power: 0.4114
Test Batch 90/313, reward: 15.270, revenue_rate: 0.8444, efficiency: 0.8368, distance: 13.8710, memory: 0.1099, power: 0.4044
Test Batch 100/313, reward: 15.911, revenue_rate: 0.8356, efficiency: 0.8292, distance: 13.5486, memory: 0.1122, power: 0.4097
Test Batch 110/313, reward: 15.178, revenue_rate: 0.8486, efficiency: 0.8413, distance: 13.9436, memory: 0.1293, power: 0.4211
Test Batch 120/313, reward: 15.499, revenue_rate: 0.8425, efficiency: 0.8363, distance: 13.7926, memory: 0.1201, power: 0.4177
Test Batch 130/313, reward: 15.455, revenue_rate: 0.8458, efficiency: 0.8363, distance: 13.7925, memory: 0.1082, power: 0.4098
Test Batch 140/313, reward: 14.465, revenue_rate: 0.8453, efficiency: 0.8364, distance: 14.4824, memory: 0.1154, power: 0.4079
Test Batch 150/313, reward: 16.397, revenue_rate: 0.8577, efficiency: 0.8507, distance: 13.9160, memory: 0.1206, power: 0.4109
Test Batch 160/313, reward: 15.776, revenue_rate: 0.8500, efficiency: 0.8415, distance: 13.7231, memory: 0.1089, power: 0.4122
Test Batch 170/313, reward: 14.923, revenue_rate: 0.8464, efficiency: 0.8361, distance: 14.1315, memory: 0.1171, power: 0.4160
Test Batch 180/313, reward: 15.959, revenue_rate: 0.8447, efficiency: 0.8353, distance: 14.0095, memory: 0.1191, power: 0.4110
Test Batch 190/313, reward: 15.731, revenue_rate: 0.8533, efficiency: 0.8451, distance: 13.9306, memory: 0.1122, power: 0.4112
Test Batch 200/313, reward: 15.831, revenue_rate: 0.8504, efficiency: 0.8443, distance: 13.7391, memory: 0.1172, power: 0.4131
Test Batch 210/313, reward: 16.028, revenue_rate: 0.8444, efficiency: 0.8378, distance: 13.6579, memory: 0.1099, power: 0.4143
Test Batch 220/313, reward: 14.646, revenue_rate: 0.8439, efficiency: 0.8366, distance: 14.1985, memory: 0.1155, power: 0.4204
Test Batch 230/313, reward: 14.792, revenue_rate: 0.8471, efficiency: 0.8389, distance: 13.8365, memory: 0.1106, power: 0.4147
Test Batch 240/313, reward: 15.703, revenue_rate: 0.8467, efficiency: 0.8388, distance: 13.8653, memory: 0.1131, power: 0.4069
Test Batch 250/313, reward: 14.995, revenue_rate: 0.8404, efficiency: 0.8344, distance: 14.2327, memory: 0.1040, power: 0.4145
Test Batch 260/313, reward: 15.689, revenue_rate: 0.8517, efficiency: 0.8443, distance: 14.2752, memory: 0.1110, power: 0.4027
Test Batch 270/313, reward: 15.892, revenue_rate: 0.8457, efficiency: 0.8376, distance: 13.7537, memory: 0.1146, power: 0.4117
Test Batch 280/313, reward: 15.955, revenue_rate: 0.8414, efficiency: 0.8339, distance: 13.6835, memory: 0.1065, power: 0.4106
Test Batch 290/313, reward: 15.581, revenue_rate: 0.8424, efficiency: 0.8340, distance: 13.5817, memory: 0.1060, power: 0.4042
Test Batch 300/313, reward: 15.827, revenue_rate: 0.8480, efficiency: 0.8395, distance: 14.0444, memory: 0.1194, power: 0.4155
Test Batch 310/313, reward: 16.005, revenue_rate: 0.8445, efficiency: 0.8355, distance: 13.7679, memory: 0.1096, power: 0.4110
Test Batch 313/313, reward: 15.435, revenue_rate: 0.8438, efficiency: 0.8344, distance: 13.8551, memory: 0.1229, power: 0.4118
Test Summary - Avg reward: 15.558±2.888, revenue_rate: 0.8477±0.0312, efficiency: 0.8400, completion_rate: 0.9908, distance: 13.9283, memory: 0.1184, power: 0.4128
Load Balance - Avg balance score: -0.3735±0.0861
Task Distribution by Satellite:
  Satellite 1: 93698 tasks (9.46%)
  Satellite 2: 44229 tasks (4.47%)
  Satellite 3: 852073 tasks (86.07%)
✅ 验证完成 - Epoch 3, reward: 15.558, revenue_rate: 0.8477, distance: 13.9283, memory: 0.1184, power: 0.4128
  ✅ 训练验证差距正常: -1.5230
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_09_22_29_56 (验证集奖励: 15.5581)
训练完成

开始测试模型...
Test Batch 10/313, reward: 15.766, revenue_rate: 0.8439, efficiency: 0.8364, distance: 13.6522, memory: 0.1241, power: 0.4204
Test Batch 20/313, reward: 15.015, revenue_rate: 0.8440, efficiency: 0.8371, distance: 13.8385, memory: 0.1359, power: 0.4112
Test Batch 30/313, reward: 16.090, revenue_rate: 0.8453, efficiency: 0.8371, distance: 13.4596, memory: 0.1075, power: 0.4065
Test Batch 40/313, reward: 15.644, revenue_rate: 0.8508, efficiency: 0.8431, distance: 14.2337, memory: 0.1209, power: 0.4167
Test Batch 50/313, reward: 15.332, revenue_rate: 0.8445, efficiency: 0.8374, distance: 14.0070, memory: 0.1104, power: 0.4025
Test Batch 60/313, reward: 16.288, revenue_rate: 0.8513, efficiency: 0.8428, distance: 13.8813, memory: 0.1128, power: 0.4081
Test Batch 70/313, reward: 15.755, revenue_rate: 0.8472, efficiency: 0.8393, distance: 13.8102, memory: 0.1054, power: 0.4179
Test Batch 80/313, reward: 15.290, revenue_rate: 0.8448, efficiency: 0.8390, distance: 14.0986, memory: 0.1283, power: 0.4167
Test Batch 90/313, reward: 16.639, revenue_rate: 0.8516, efficiency: 0.8437, distance: 13.7798, memory: 0.1165, power: 0.4151
Test Batch 100/313, reward: 16.646, revenue_rate: 0.8472, efficiency: 0.8390, distance: 13.6641, memory: 0.1267, power: 0.4084
Test Batch 110/313, reward: 15.347, revenue_rate: 0.8393, efficiency: 0.8314, distance: 13.5694, memory: 0.1138, power: 0.4071
Test Batch 120/313, reward: 15.629, revenue_rate: 0.8429, efficiency: 0.8355, distance: 13.7000, memory: 0.1233, power: 0.4100
Test Batch 130/313, reward: 14.646, revenue_rate: 0.8463, efficiency: 0.8400, distance: 14.2911, memory: 0.1076, power: 0.4075
Test Batch 140/313, reward: 15.797, revenue_rate: 0.8487, efficiency: 0.8406, distance: 14.0238, memory: 0.1143, power: 0.4044
Test Batch 150/313, reward: 15.877, revenue_rate: 0.8442, efficiency: 0.8353, distance: 13.6901, memory: 0.1171, power: 0.4148
Test Batch 160/313, reward: 14.690, revenue_rate: 0.8381, efficiency: 0.8287, distance: 14.1716, memory: 0.1256, power: 0.4031
Test Batch 170/313, reward: 15.855, revenue_rate: 0.8600, efficiency: 0.8528, distance: 13.8750, memory: 0.1201, power: 0.4259
Test Batch 180/313, reward: 15.934, revenue_rate: 0.8481, efficiency: 0.8417, distance: 13.7866, memory: 0.1181, power: 0.4186
Test Batch 190/313, reward: 16.545, revenue_rate: 0.8437, efficiency: 0.8363, distance: 13.3838, memory: 0.1200, power: 0.4144
Test Batch 200/313, reward: 15.133, revenue_rate: 0.8469, efficiency: 0.8390, distance: 14.0373, memory: 0.1279, power: 0.4231
Test Batch 210/313, reward: 16.146, revenue_rate: 0.8400, efficiency: 0.8308, distance: 13.5636, memory: 0.1215, power: 0.4130
Test Batch 220/313, reward: 15.129, revenue_rate: 0.8435, efficiency: 0.8337, distance: 14.3440, memory: 0.1224, power: 0.4161
Test Batch 230/313, reward: 14.536, revenue_rate: 0.8383, efficiency: 0.8295, distance: 14.2832, memory: 0.1221, power: 0.4089
Test Batch 240/313, reward: 14.668, revenue_rate: 0.8395, efficiency: 0.8319, distance: 13.8965, memory: 0.1114, power: 0.4100
Test Batch 250/313, reward: 14.789, revenue_rate: 0.8463, efficiency: 0.8378, distance: 14.0719, memory: 0.1250, power: 0.4072
Test Batch 260/313, reward: 14.875, revenue_rate: 0.8441, efficiency: 0.8364, distance: 13.9105, memory: 0.1195, power: 0.4064
Test Batch 270/313, reward: 14.484, revenue_rate: 0.8475, efficiency: 0.8385, distance: 14.2920, memory: 0.1098, power: 0.4100
Test Batch 280/313, reward: 16.690, revenue_rate: 0.8581, efficiency: 0.8502, distance: 14.0044, memory: 0.1130, power: 0.4165
Test Batch 290/313, reward: 15.424, revenue_rate: 0.8550, efficiency: 0.8481, distance: 14.3827, memory: 0.1359, power: 0.4216
Test Batch 300/313, reward: 15.767, revenue_rate: 0.8461, efficiency: 0.8385, distance: 13.8405, memory: 0.1263, power: 0.4125
Test Batch 310/313, reward: 15.898, revenue_rate: 0.8656, efficiency: 0.8561, distance: 13.8739, memory: 0.1291, power: 0.4205
Test Batch 313/313, reward: 14.637, revenue_rate: 0.8352, efficiency: 0.8279, distance: 13.9752, memory: 0.1149, power: 0.4088
Test Summary - Avg reward: 15.598±2.899, revenue_rate: 0.8471±0.0319, efficiency: 0.8394, completion_rate: 0.9908, distance: 13.9155, memory: 0.1183, power: 0.4125
Load Balance - Avg balance score: -0.3729±0.0857
Task Distribution by Satellite:
  Satellite 1: 93755 tasks (9.47%)
  Satellite 2: 44391 tasks (4.48%)
  Satellite 3: 851854 tasks (86.05%)
测试完成 - 平均奖励: 15.598, 平均星座收益率: 0.8471
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_09_22_29_56
   平均奖励: 15.5982
   收益率: 0.8471

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_11_03_39_04
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 7.3795, Loss: 50.5502, Revenue: 0.4350, LoadBalance: 0.4674, Tasks: [S0:746(44.0%), S1:241(14.2%), S2:709(41.8%)], ActorGrad: 64.5023, CriticGrad: 332.4796, Advantage: μ=1.568, σ=0.829, range=[-0.16, 3.86]
Epoch 1, Batch 50/3125, loss: 47.729↓, reward: 9.495↑, critic_reward: 3.599, revenue_rate: 0.5753, distance: 10.8118, memory: 0.0475, power: 0.2755, lr: 0.000050, took: 388.829s
Batch 50: Reward: 11.3357, Loss: 68.1411, Revenue: 0.6336, LoadBalance: 0.0000, Tasks: [S0:441(18.4%), S1:22(0.9%), S2:1937(80.7%)], ActorGrad: 64.2872, CriticGrad: 91.6115, Advantage: μ=1.540, σ=0.881, range=[-0.15, 3.16]
Epoch 1, Batch 100/3125, loss: 63.692↓, reward: 11.486↓, critic_reward: 4.288, revenue_rate: 0.6519, distance: 11.7121, memory: 0.0349, power: 0.3111, lr: 0.000050, took: 404.410s
Batch 100: Reward: 11.2358, Loss: 58.7526, Revenue: 0.6641, LoadBalance: 0.0000, Tasks: [S0:453(18.1%), S1:16(0.6%), S2:2027(81.2%)], ActorGrad: 40.5855, CriticGrad: 92.4859, Advantage: μ=1.622, σ=0.715, range=[-0.09, 3.32]
Epoch 1, Batch 150/3125, loss: 61.091↑, reward: 11.503↑, critic_reward: 4.514, revenue_rate: 0.6605, distance: 11.9592, memory: 0.0374, power: 0.3152, lr: 0.000050, took: 423.835s
Batch 150: Reward: 11.0567, Loss: 52.5079, Revenue: 0.6758, LoadBalance: 0.0000, Tasks: [S0:302(11.7%), S1:18(0.7%), S2:2272(87.7%)], ActorGrad: 39.1759, CriticGrad: 73.0710, Advantage: μ=1.598, σ=0.768, range=[0.29, 2.98]
Epoch 1, Batch 200/3125, loss: 65.856↑, reward: 11.861↑, critic_reward: 4.634, revenue_rate: 0.7151, distance: 13.2930, memory: 0.0606, power: 0.3428, lr: 0.000050, took: 460.893s
Batch 200: Reward: 11.0576, Loss: 57.2181, Revenue: 0.7413, LoadBalance: 0.0000, Tasks: [S0:142(4.8%), S1:14(0.5%), S2:2788(94.7%)], ActorGrad: 28.4095, CriticGrad: 90.8880, Advantage: μ=1.458, σ=1.015, range=[-0.36, 3.52]
Epoch 1, Batch 250/3125, loss: 82.797↑, reward: 12.970↑, critic_reward: 4.748, revenue_rate: 0.7817, distance: 14.5158, memory: 0.0898, power: 0.3762, lr: 0.000050, took: 504.746s
Batch 250: Reward: 14.3109, Loss: 101.2574, Revenue: 0.8212, LoadBalance: 0.0000, Tasks: [S0:48(1.6%), S1:4(0.1%), S2:2956(98.3%)], ActorGrad: 28.7598, CriticGrad: 108.7168, Advantage: μ=1.667, σ=0.597, range=[0.48, 3.18]
Epoch 1, Batch 300/3125, loss: 98.852↑, reward: 14.068↑, critic_reward: 4.887, revenue_rate: 0.8210, distance: 14.9271, memory: 0.1083, power: 0.3977, lr: 0.000050, took: 506.592s
Batch 300: Reward: 13.8747, Loss: 90.8667, Revenue: 0.8122, LoadBalance: 0.0000, Tasks: [S0:32(1.0%), S1:8(0.3%), S2:3032(98.7%)], ActorGrad: 25.7258, CriticGrad: 112.2404, Advantage: μ=1.665, σ=0.604, range=[0.54, 3.21]
Epoch 1, Batch 350/3125, loss: 108.855↑, reward: 14.710↑, critic_reward: 4.985, revenue_rate: 0.8407, distance: 15.1013, memory: 0.1140, power: 0.4081, lr: 0.000050, took: 508.982s
Batch 350: Reward: 15.0123, Loss: 106.7567, Revenue: 0.8613, LoadBalance: 0.0000, Tasks: [S0:18(0.6%), S1:6(0.2%), S2:3080(99.2%)], ActorGrad: 27.7611, CriticGrad: 117.1603, Advantage: μ=1.694, σ=0.514, range=[0.59, 2.84]
Epoch 1, Batch 400/3125, loss: 111.949↓, reward: 15.055↓, critic_reward: 5.095, revenue_rate: 0.8479, distance: 14.9840, memory: 0.1170, power: 0.4116, lr: 0.000050, took: 501.071s
Batch 400: Reward: 15.1344, Loss: 112.5040, Revenue: 0.8346, LoadBalance: 0.0000, Tasks: [S0:23(0.8%), S1:6(0.2%), S2:2947(99.0%)], ActorGrad: 25.8492, CriticGrad: 133.2738, Advantage: μ=1.691, σ=0.525, range=[0.50, 2.67]
Epoch 1, Batch 450/3125, loss: 108.811↓, reward: 14.960↑, critic_reward: 5.200, revenue_rate: 0.8457, distance: 15.0510, memory: 0.1192, power: 0.4101, lr: 0.000050, took: 506.765s
Batch 450: Reward: 14.8269, Loss: 102.1962, Revenue: 0.8546, LoadBalance: 0.0000, Tasks: [S0:24(0.8%), S1:3(0.1%), S2:3141(99.1%)], ActorGrad: 33.4528, CriticGrad: 110.7780, Advantage: μ=1.667, σ=0.598, range=[0.53, 2.84]
Epoch 1, Batch 500/3125, loss: 110.426↑, reward: 15.168↓, critic_reward: 5.282, revenue_rate: 0.8536, distance: 15.1137, memory: 0.1210, power: 0.4138, lr: 0.000050, took: 504.823s
Batch 500: Reward: 15.3387, Loss: 116.1900, Revenue: 0.8625, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:6(0.2%), S2:3093(99.6%)], ActorGrad: 24.3153, CriticGrad: 111.9730, Advantage: μ=1.702, σ=0.485, range=[0.72, 2.87]
Epoch 1, Batch 550/3125, loss: 111.295↑, reward: 15.320↑, critic_reward: 5.343, revenue_rate: 0.8629, distance: 15.2467, memory: 0.1260, power: 0.4194, lr: 0.000050, took: 502.325s
Batch 550: Reward: 16.1623, Loss: 125.8906, Revenue: 0.8640, LoadBalance: 0.0000, Tasks: [S0:13(0.4%), S1:2(0.1%), S2:3153(99.5%)], ActorGrad: 24.4820, CriticGrad: 126.4221, Advantage: μ=1.711, σ=0.450, range=[0.84, 2.88]
Epoch 1, Batch 600/3125, loss: 110.490↓, reward: 15.371↓, critic_reward: 5.431, revenue_rate: 0.8637, distance: 15.2134, memory: 0.1270, power: 0.4202, lr: 0.000050, took: 505.561s
Batch 600: Reward: 15.4373, Loss: 109.7494, Revenue: 0.8589, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:5(0.2%), S2:3094(99.7%)], ActorGrad: 23.3453, CriticGrad: 113.9843, Advantage: μ=1.680, σ=0.558, range=[0.72, 3.21]
Epoch 1, Batch 650/3125, loss: 110.268↓, reward: 15.441↓, critic_reward: 5.544, revenue_rate: 0.8638, distance: 15.2403, memory: 0.1278, power: 0.4193, lr: 0.000050, took: 502.780s
Batch 650: Reward: 15.4570, Loss: 101.6992, Revenue: 0.8584, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:9(0.3%), S2:3158(99.7%)], ActorGrad: 22.7981, CriticGrad: 115.7395, Advantage: μ=1.701, σ=0.489, range=[0.66, 2.53]
Epoch 1, Batch 700/3125, loss: 110.228↓, reward: 15.568↓, critic_reward: 5.646, revenue_rate: 0.8662, distance: 15.2141, memory: 0.1264, power: 0.4191, lr: 0.000050, took: 502.121s
Batch 700: Reward: 14.7570, Loss: 94.7111, Revenue: 0.8750, LoadBalance: 0.0000, Tasks: [S0:7(0.2%), S1:3(0.1%), S2:3158(99.7%)], ActorGrad: 22.6754, CriticGrad: 105.1906, Advantage: μ=1.634, σ=0.684, range=[-0.26, 2.95]
Epoch 1, Batch 750/3125, loss: 109.119↑, reward: 15.646↑, critic_reward: 5.754, revenue_rate: 0.8673, distance: 15.1531, memory: 0.1251, power: 0.4215, lr: 0.000050, took: 499.456s
Batch 750: Reward: 15.4894, Loss: 113.9126, Revenue: 0.8697, LoadBalance: 0.0000, Tasks: [S0:8(0.3%), S1:2(0.1%), S2:3126(99.7%)], ActorGrad: 21.6392, CriticGrad: 111.3397, Advantage: μ=1.622, σ=0.714, range=[0.48, 3.22]
Epoch 1, Batch 800/3125, loss: 106.091↓, reward: 15.505↑, critic_reward: 5.828, revenue_rate: 0.8667, distance: 15.2430, memory: 0.1274, power: 0.4202, lr: 0.000050, took: 503.464s
Batch 800: Reward: 14.6703, Loss: 90.8929, Revenue: 0.8651, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:2(0.1%), S2:3160(99.7%)], ActorGrad: 20.7390, CriticGrad: 105.2814, Advantage: μ=1.636, σ=0.680, range=[0.24, 3.27]
Epoch 1, Batch 850/3125, loss: 105.510↑, reward: 15.505↑, critic_reward: 5.888, revenue_rate: 0.8686, distance: 15.3132, memory: 0.1285, power: 0.4210, lr: 0.000050, took: 506.640s
Batch 850: Reward: 16.3323, Loss: 116.6713, Revenue: 0.8719, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:2(0.1%), S2:3099(99.8%)], ActorGrad: 18.7304, CriticGrad: 120.4522, Advantage: μ=1.684, σ=0.546, range=[0.80, 2.89]
Epoch 1, Batch 900/3125, loss: 105.357↓, reward: 15.696↓, critic_reward: 5.976, revenue_rate: 0.8718, distance: 15.2454, memory: 0.1281, power: 0.4228, lr: 0.000050, took: 500.203s
Batch 900: Reward: 16.4778, Loss: 121.3304, Revenue: 0.8715, LoadBalance: 0.0000, Tasks: [S0:7(0.2%), S1:2(0.1%), S2:3063(99.7%)], ActorGrad: 18.5586, CriticGrad: 123.2887, Advantage: μ=1.702, σ=0.487, range=[0.62, 2.82]
Epoch 1, Batch 950/3125, loss: 105.304↓, reward: 15.727↓, critic_reward: 6.057, revenue_rate: 0.8710, distance: 15.2193, memory: 0.1282, power: 0.4232, lr: 0.000050, took: 502.845s
Batch 950: Reward: 14.6068, Loss: 85.0220, Revenue: 0.8638, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:4(0.1%), S2:3094(99.7%)], ActorGrad: 19.3090, CriticGrad: 102.5152, Advantage: μ=1.651, σ=0.643, range=[0.31, 2.94]
Epoch 1, Batch 1000/3125, loss: 102.658↑, reward: 15.691↑, critic_reward: 6.117, revenue_rate: 0.8709, distance: 15.2099, memory: 0.1274, power: 0.4224, lr: 0.000050, took: 499.225s
Batch 1000: Reward: 14.9791, Loss: 90.9183, Revenue: 0.8663, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:4(0.1%), S2:3065(99.8%)], ActorGrad: 20.2444, CriticGrad: 104.8233, Advantage: μ=1.639, σ=0.673, range=[-0.06, 2.47]
Epoch 1, Batch 1050/3125, loss: 99.893↓, reward: 15.613↓, critic_reward: 6.201, revenue_rate: 0.8718, distance: 15.2809, memory: 0.1275, power: 0.4241, lr: 0.000050, took: 504.005s
Batch 1050: Reward: 15.1067, Loss: 89.4381, Revenue: 0.8586, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:4(0.1%), S2:3064(99.7%)], ActorGrad: 20.5739, CriticGrad: 105.1690, Advantage: μ=1.615, σ=0.731, range=[-0.13, 2.96]
Epoch 1, Batch 1100/3125, loss: 97.142↓, reward: 15.604↑, critic_reward: 6.330, revenue_rate: 0.8704, distance: 15.2768, memory: 0.1268, power: 0.4221, lr: 0.000050, took: 498.703s
Batch 1100: Reward: 16.0112, Loss: 104.7867, Revenue: 0.8773, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:2(0.1%), S2:3100(99.9%)], ActorGrad: 21.0394, CriticGrad: 119.3247, Advantage: μ=1.684, σ=0.546, range=[-0.08, 2.58]
Epoch 1, Batch 1150/3125, loss: 96.623↑, reward: 15.639↑, critic_reward: 6.385, revenue_rate: 0.8729, distance: 15.2885, memory: 0.1298, power: 0.4238, lr: 0.000050, took: 502.085s
Batch 1150: Reward: 15.0142, Loss: 87.4194, Revenue: 0.8608, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:5(0.2%), S2:3098(99.8%)], ActorGrad: 18.9251, CriticGrad: 102.1234, Advantage: μ=1.606, σ=0.751, range=[0.10, 3.51]
Epoch 1, Batch 1200/3125, loss: 96.399↑, reward: 15.697↑, critic_reward: 6.483, revenue_rate: 0.8734, distance: 15.2773, memory: 0.1311, power: 0.4247, lr: 0.000050, took: 500.047s
Batch 1200: Reward: 16.4890, Loss: 105.3143, Revenue: 0.8829, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:0(0.0%), S2:3165(99.9%)], ActorGrad: 19.7524, CriticGrad: 121.0249, Advantage: μ=1.680, σ=0.559, range=[0.20, 2.57]
Epoch 1, Batch 1250/3125, loss: 93.412↓, reward: 15.676↓, critic_reward: 6.590, revenue_rate: 0.8716, distance: 15.2196, memory: 0.1286, power: 0.4238, lr: 0.000050, took: 500.593s
Batch 1250: Reward: 15.9565, Loss: 106.5492, Revenue: 0.8734, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:2(0.1%), S2:3165(99.9%)], ActorGrad: 19.9807, CriticGrad: 115.1201, Advantage: μ=1.660, σ=0.618, range=[0.32, 2.73]
Epoch 1, Batch 1300/3125, loss: 92.170↓, reward: 15.641↓, critic_reward: 6.649, revenue_rate: 0.8717, distance: 15.2735, memory: 0.1295, power: 0.4229, lr: 0.000050, took: 504.294s
Batch 1300: Reward: 15.6949, Loss: 90.1086, Revenue: 0.8814, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:1(0.0%), S2:3135(100.0%)], ActorGrad: 22.5015, CriticGrad: 109.9579, Advantage: μ=1.709, σ=0.460, range=[0.92, 2.45]
Epoch 1, Batch 1350/3125, loss: 89.379↓, reward: 15.575↓, critic_reward: 6.711, revenue_rate: 0.8731, distance: 15.3729, memory: 0.1305, power: 0.4247, lr: 0.000050, took: 502.998s
Batch 1350: Reward: 16.3053, Loss: 94.7780, Revenue: 0.8649, LoadBalance: 0.0000, Tasks: [S0:6(0.2%), S1:0(0.0%), S2:3066(99.8%)], ActorGrad: 21.1272, CriticGrad: 117.0913, Advantage: μ=1.664, σ=0.605, range=[0.16, 2.92]
Epoch 1, Batch 1400/3125, loss: 92.370↑, reward: 15.776↓, critic_reward: 6.779, revenue_rate: 0.8715, distance: 15.2044, memory: 0.1279, power: 0.4226, lr: 0.000050, took: 503.591s
Batch 1400: Reward: 15.0528, Loss: 76.9849, Revenue: 0.8707, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:0(0.0%), S2:3103(100.0%)], ActorGrad: 19.9187, CriticGrad: 101.0935, Advantage: μ=1.621, σ=0.716, range=[-0.32, 2.84]
Epoch 1, Batch 1450/3125, loss: 89.738↑, reward: 15.734↑, critic_reward: 6.884, revenue_rate: 0.8720, distance: 15.2119, memory: 0.1298, power: 0.4236, lr: 0.000050, took: 497.293s
Batch 1450: Reward: 16.0872, Loss: 90.8109, Revenue: 0.8771, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:2(0.1%), S2:3070(99.9%)], ActorGrad: 18.8882, CriticGrad: 113.6335, Advantage: μ=1.690, σ=0.528, range=[0.75, 2.89]
Epoch 1, Batch 1500/3125, loss: 90.233↑, reward: 15.820↑, critic_reward: 6.939, revenue_rate: 0.8723, distance: 15.2430, memory: 0.1270, power: 0.4234, lr: 0.000050, took: 502.016s
Batch 1500: Reward: 15.5144, Loss: 82.5442, Revenue: 0.8563, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:6(0.2%), S2:2999(99.7%)], ActorGrad: 18.3734, CriticGrad: 107.4415, Advantage: μ=1.661, σ=0.614, range=[0.34, 2.66]
Epoch 1, Batch 1550/3125, loss: 86.631↑, reward: 15.712↑, critic_reward: 7.015, revenue_rate: 0.8737, distance: 15.2981, memory: 0.1310, power: 0.4251, lr: 0.000050, took: 501.190s
Batch 1550: Reward: 15.1957, Loss: 82.3866, Revenue: 0.8664, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:2(0.1%), S2:3098(99.8%)], ActorGrad: 18.7301, CriticGrad: 100.6766, Advantage: μ=1.648, σ=0.649, range=[0.51, 3.06]
Epoch 1, Batch 1600/3125, loss: 82.154↑, reward: 15.590↑, critic_reward: 7.148, revenue_rate: 0.8733, distance: 15.3054, memory: 0.1308, power: 0.4236, lr: 0.000050, took: 502.050s
Batch 1600: Reward: 15.7447, Loss: 83.2405, Revenue: 0.8748, LoadBalance: 0.0000, Tasks: [S0:3(0.1%), S1:1(0.0%), S2:3164(99.9%)], ActorGrad: 18.6810, CriticGrad: 104.4704, Advantage: μ=1.664, σ=0.606, range=[0.41, 2.83]
Epoch 1, Batch 1650/3125, loss: 84.770↑, reward: 15.761↑, critic_reward: 7.157, revenue_rate: 0.8725, distance: 15.2343, memory: 0.1296, power: 0.4236, lr: 0.000050, took: 498.577s
Batch 1650: Reward: 15.6870, Loss: 90.0641, Revenue: 0.8505, LoadBalance: 0.0000, Tasks: [S0:4(0.1%), S1:3(0.1%), S2:2969(99.8%)], ActorGrad: 17.3544, CriticGrad: 105.0940, Advantage: μ=1.630, σ=0.695, range=[-0.05, 2.72]
Epoch 1, Batch 1700/3125, loss: 84.423↓, reward: 15.858↑, critic_reward: 7.307, revenue_rate: 0.8760, distance: 15.2986, memory: 0.1300, power: 0.4252, lr: 0.000050, took: 502.006s
Batch 1700: Reward: 16.1974, Loss: 88.4517, Revenue: 0.8716, LoadBalance: 0.0000, Tasks: [S0:5(0.2%), S1:2(0.1%), S2:3001(99.8%)], ActorGrad: 20.4688, CriticGrad: 111.3187, Advantage: μ=1.672, σ=0.584, range=[0.55, 3.26]
Epoch 1, Batch 1750/3125, loss: 82.376↓, reward: 15.832↓, critic_reward: 7.382, revenue_rate: 0.8734, distance: 15.2213, memory: 0.1308, power: 0.4232, lr: 0.000050, took: 502.537s
Batch 1750: Reward: 15.2135, Loss: 64.6038, Revenue: 0.8817, LoadBalance: 0.0000, Tasks: [S0:0(0.0%), S1:0(0.0%), S2:3168(100.0%)], ActorGrad: 19.6333, CriticGrad: 96.7786, Advantage: μ=1.664, σ=0.605, range=[0.40, 2.92]
Epoch 1, Batch 1800/3125, loss: 79.150↑, reward: 15.734↑, critic_reward: 7.494, revenue_rate: 0.8754, distance: 15.3047, memory: 0.1317, power: 0.4259, lr: 0.000050, took: 498.080s
Batch 1800: Reward: 17.2092, Loss: 103.7807, Revenue: 0.8893, LoadBalance: 0.0000, Tasks: [S0:1(0.0%), S1:3(0.1%), S2:3100(99.9%)], ActorGrad: 19.9248, CriticGrad: 123.0935, Advantage: μ=1.674, σ=0.578, range=[0.64, 3.11]
Epoch 1, Batch 1850/3125, loss: 74.498↓, reward: 15.544↓, critic_reward: 7.565, revenue_rate: 0.8760, distance: 15.4370, memory: 0.1318, power: 0.4262, lr: 0.000050, took: 501.716s
Batch 1850: Reward: 16.4467, Loss: 92.4172, Revenue: 0.8877, LoadBalance: 0.0000, Tasks: [S0:2(0.1%), S1:1(0.0%), S2:3165(99.9%)], ActorGrad: 18.6177, CriticGrad: 111.5874, Advantage: μ=1.660, σ=0.617, range=[0.48, 2.76]
