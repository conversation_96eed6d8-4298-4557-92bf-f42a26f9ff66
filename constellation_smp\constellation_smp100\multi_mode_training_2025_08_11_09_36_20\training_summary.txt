🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_11_09_36_20
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_11_09_36_23
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: -3.6145, Loss: 93.6997, Revenue: 0.4493, LoadBalance: -1.0827, Tasks: [S0:798(43.8%), S1:251(13.8%), S2:775(42.5%)], ActorGrad: 117.2834, CriticGrad: 262.5769, Advantage: μ=-0.894, σ=1.549, range=[-3.70, 4.42]

📊 Epoch 1 训练统计:
  平均奖励: 11.6969
  平均损失: 216.2183
  平均收益率: 0.3237
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 11.224, revenue_rate: 0.3121, efficiency: 0.0992, distance: 4.4925, memory: -0.0869, power: 0.1378
Test Summary - Avg reward: 14.430±11.237, revenue_rate: 0.3306±0.0495, efficiency: 0.1312, completion_rate: 0.3924, distance: 5.8482, memory: 0.0216, power: 0.1549
Load Balance - Avg balance score: -0.5267±0.2738
Task Distribution by Satellite:
  Satellite 1: 1250 tasks (32.59%)
  Satellite 2: 1233 tasks (32.14%)
  Satellite 3: 1353 tasks (35.27%)
✅ 验证完成 - Epoch 1, reward: 14.430, revenue_rate: 0.3306, distance: 5.8482, memory: 0.0216, power: 0.1549
  ⚠️ 欠拟合: 训练验证差距 = -2.7332
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_11_09_36_23 (验证集奖励: 14.4300)

开始训练 Epoch 2/3
Batch 0: Reward: 10.6873, Loss: 199.8446, Revenue: 0.2539, LoadBalance: -0.6196, Tasks: [S0:295(31.8%), S1:303(32.7%), S2:330(35.6%)], ActorGrad: 40.1510, CriticGrad: 106.3193, Advantage: μ=0.850, σ=1.575, range=[-2.60, 2.74]

📊 Epoch 2 训练统计:
  平均奖励: 14.3920
  平均损失: 225.3027
  平均收益率: 0.3132
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 13.257, revenue_rate: 0.3196, efficiency: 0.1206, distance: 5.3062, memory: -0.0071, power: 0.1539
Test Summary - Avg reward: 10.708±11.674, revenue_rate: 0.3035±0.0401, efficiency: 0.1102, completion_rate: 0.3621, distance: 5.1691, memory: 0.0453, power: 0.1472
Load Balance - Avg balance score: -0.6010±0.2969
Task Distribution by Satellite:
  Satellite 1: 1013 tasks (28.62%)
  Satellite 2: 1374 tasks (38.81%)
  Satellite 3: 1153 tasks (32.57%)
✅ 验证完成 - Epoch 2, reward: 10.708, revenue_rate: 0.3035, distance: 5.1691, memory: 0.0453, power: 0.1472
  ⚠️ 过拟合: 训练验证差距 = 3.6840

开始训练 Epoch 3/3
Batch 0: Reward: 17.2990, Loss: 266.5833, Revenue: 0.3147, LoadBalance: -0.4424, Tasks: [S0:376(33.6%), S1:389(34.7%), S2:355(31.7%)], ActorGrad: 38.9501, CriticGrad: 170.3764, Advantage: μ=1.427, σ=1.059, range=[-0.55, 2.69]

📊 Epoch 3 训练统计:
  平均奖励: 14.4119
  平均损失: 223.7989
  平均收益率: 0.3150
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 12.500, revenue_rate: 0.2931, efficiency: 0.0997, distance: 4.3625, memory: -0.1083, power: 0.1391
Test Summary - Avg reward: 13.769±10.245, revenue_rate: 0.3039±0.0393, efficiency: 0.1090, completion_rate: 0.3583, distance: 5.2886, memory: 0.0350, power: 0.1464
Load Balance - Avg balance score: -0.5287±0.2484
Task Distribution by Satellite:
  Satellite 1: 1150 tasks (32.93%)
  Satellite 2: 1146 tasks (32.82%)
  Satellite 3: 1196 tasks (34.25%)
✅ 验证完成 - Epoch 3, reward: 13.769, revenue_rate: 0.3039, distance: 5.2886, memory: 0.0350, power: 0.1464
  ✅ 训练验证差距正常: 0.6428
训练完成

开始测试模型...
Test Batch 4/4, reward: 12.938, revenue_rate: 0.2632, efficiency: 0.0816, distance: 4.2837, memory: -0.0346, power: 0.1213
Test Summary - Avg reward: 14.462±10.456, revenue_rate: 0.3305±0.0527, efficiency: 0.1278, completion_rate: 0.3821, distance: 5.7164, memory: 0.0336, power: 0.1569
Load Balance - Avg balance score: -0.4975±0.2623
Task Distribution by Satellite:
  Satellite 1: 1207 tasks (32.31%)
  Satellite 2: 1198 tasks (32.07%)
  Satellite 3: 1331 tasks (35.63%)
测试完成 - 平均奖励: 14.462, 平均星座收益率: 0.3305
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_11_09_36_23
   平均奖励: 14.4620
   收益率: 0.3305

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_11_09_44_46
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 4.3113, Loss: 23.2455, Revenue: 0.4493, LoadBalance: -1.0827, Tasks: [S0:798(43.8%), S1:251(13.8%), S2:775(42.5%)], ActorGrad: 78.0527, CriticGrad: 192.9680, Advantage: μ=1.111, σ=1.397, range=[-1.21, 4.10]

📊 Epoch 1 训练统计:
  平均奖励: 8.1404
  平均损失: 40.6187
  平均收益率: 0.3210
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 6.660, revenue_rate: 0.3083, efficiency: 0.1171, distance: 5.4350, memory: 0.0834, power: 0.1473
Test Summary - Avg reward: 8.137±4.527, revenue_rate: 0.3010±0.0387, efficiency: 0.1065, completion_rate: 0.3529, distance: 5.1139, memory: 0.0378, power: 0.1419
Load Balance - Avg balance score: -0.5567±0.3033
Task Distribution by Satellite:
  Satellite 1: 1020 tasks (29.62%)
  Satellite 2: 1137 tasks (33.01%)
  Satellite 3: 1287 tasks (37.37%)
✅ 验证完成 - Epoch 1, reward: 8.137, revenue_rate: 0.3010, distance: 5.1139, memory: 0.0378, power: 0.1419
  ✅ 训练验证差距正常: 0.0033
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_11_09_44_46 (验证集奖励: 8.1371)

开始训练 Epoch 2/3
Batch 0: Reward: 8.3399, Loss: 33.5196, Revenue: 0.3297, LoadBalance: -0.4977, Tasks: [S0:364(29.2%), S1:426(34.1%), S2:458(36.7%)], ActorGrad: 42.7904, CriticGrad: 53.4651, Advantage: μ=1.309, σ=1.207, range=[-1.63, 3.38]

📊 Epoch 2 训练统计:
  平均奖励: 8.1296
  平均损失: 32.9261
  平均收益率: 0.3181
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 8.416, revenue_rate: 0.2371, efficiency: 0.0664, distance: 3.3994, memory: 0.0317, power: 0.1182
Test Summary - Avg reward: 8.702±3.608, revenue_rate: 0.3175±0.0437, efficiency: 0.1183, completion_rate: 0.3715, distance: 5.3061, memory: 0.0436, power: 0.1494
Load Balance - Avg balance score: -0.5205±0.2524
Task Distribution by Satellite:
  Satellite 1: 1291 tasks (35.58%)
  Satellite 2: 1296 tasks (35.72%)
  Satellite 3: 1041 tasks (28.69%)
✅ 验证完成 - Epoch 2, reward: 8.702, revenue_rate: 0.3175, distance: 5.3061, memory: 0.0436, power: 0.1494
  ✅ 训练验证差距正常: -0.5722
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_11_09_44_46 (验证集奖励: 8.7018)

开始训练 Epoch 3/3
Batch 0: Reward: 10.2203, Loss: 44.7922, Revenue: 0.3386, LoadBalance: -0.4655, Tasks: [S0:425(35.0%), S1:402(33.1%), S2:389(32.0%)], ActorGrad: 43.5791, CriticGrad: 77.6873, Advantage: μ=1.530, σ=0.899, range=[-0.83, 3.29]

📊 Epoch 3 训练统计:
  平均奖励: 8.3609
  平均损失: 34.2493
  平均收益率: 0.3128
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 6.151, revenue_rate: 0.2711, efficiency: 0.0786, distance: 4.0215, memory: -0.0308, power: 0.1266
Test Summary - Avg reward: 8.244±4.098, revenue_rate: 0.3370±0.0468, efficiency: 0.1342, completion_rate: 0.3942, distance: 5.8808, memory: 0.0433, power: 0.1612
Load Balance - Avg balance score: -0.5912±0.2842
Task Distribution by Satellite:
  Satellite 1: 1105 tasks (28.66%)
  Satellite 2: 1345 tasks (34.88%)
  Satellite 3: 1406 tasks (36.46%)
✅ 验证完成 - Epoch 3, reward: 8.244, revenue_rate: 0.3370, distance: 5.8808, memory: 0.0433, power: 0.1612
  ✅ 训练验证差距正常: 0.1170
训练完成

开始测试模型...
Test Batch 4/4, reward: 6.125, revenue_rate: 0.2647, efficiency: 0.0794, distance: 3.9558, memory: 0.0421, power: 0.1233
Test Summary - Avg reward: 8.695±3.579, revenue_rate: 0.3402±0.0453, efficiency: 0.1368, completion_rate: 0.4011, distance: 5.9853, memory: 0.0540, power: 0.1635
Load Balance - Avg balance score: -0.5381±0.2479
Task Distribution by Satellite:
  Satellite 1: 1077 tasks (27.45%)
  Satellite 2: 1376 tasks (35.07%)
  Satellite 3: 1471 tasks (37.49%)
测试完成 - 平均奖励: 8.695, 平均星座收益率: 0.3402
✅ 模式 COMPETITIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_11_09_44_46
   平均奖励: 8.6951
   收益率: 0.3402

🚀 [3/3] 开始训练模式: HYBRID

============================================================
开始训练星座模式: HYBRID
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: hybrid
verbose: True
2025_08_11_09_52_53
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: -0.2177, Loss: 39.9542, Revenue: 0.4493, LoadBalance: -1.0827, Tasks: [S0:798(43.8%), S1:251(13.8%), S2:775(42.5%)], ActorGrad: 117.3387, CriticGrad: 84.0208, Advantage: μ=-0.419, σ=1.745, range=[-3.54, 5.21]
