"""
诊断资源约束问题的脚本
检查为什么单一卫星能完成大量任务而不违反资源约束
"""
import torch
import numpy as np
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward
from constellation_smp.model_factory import ModelManager
from hyperparameter import args

def analyze_resource_constraints():
    """分析资源约束的实际限制能力"""
    print("🔍 分析资源约束的实际限制能力...")
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=100,
        num_nodes=args.num_nodes,
        num_satellites=args.num_satellites,
        memory_total=args.memory_total,
        power_total=args.power_total
    )
    
    static, dynamic, _ = test_data[0]
    
    print(f"资源配置:")
    print(f"  每颗卫星内存总量: {args.memory_total}")
    print(f"  每颗卫星能量总量: {args.power_total}")
    print(f"  任务数量: {args.num_nodes}")
    print(f"  卫星数量: {args.num_satellites}")
    
    # 分析任务的资源需求
    memory_needs = static[5, :].numpy()  # 内存需求
    power_needs = static[6, :].numpy()   # 能量需求
    task_durations = static[3, :].numpy()  # 任务持续时间
    
    print(f"\n任务资源需求分析:")
    print(f"  内存需求范围: [{memory_needs.min():.4f}, {memory_needs.max():.4f}]")
    print(f"  内存需求平均: {memory_needs.mean():.4f}")
    print(f"  能量需求范围: [{power_needs.min():.4f}, {power_needs.max():.4f}]")
    print(f"  能量需求平均: {power_needs.mean():.4f}")
    print(f"  任务持续时间范围: [{task_durations.min():.4f}, {task_durations.max():.4f}]")
    
    # 计算理论上单颗卫星能完成的最大任务数
    total_memory_needed = memory_needs.sum()
    total_power_needed = power_needs.sum()
    
    print(f"\n理论资源需求:")
    print(f"  完成所有任务需要的总内存: {total_memory_needed:.4f}")
    print(f"  完成所有任务需要的总能量: {total_power_needed:.4f}")
    
    # 检查单颗卫星是否能完成所有任务
    memory_ratio = total_memory_needed / args.memory_total
    power_ratio = total_power_needed / args.power_total
    
    print(f"\n资源约束分析:")
    print(f"  内存约束倍数: {memory_ratio:.2f}x (>1表示资源不足)")
    print(f"  能量约束倍数: {power_ratio:.2f}x (>1表示资源不足)")
    
    if memory_ratio > 1 or power_ratio > 1:
        print("  ❌ 理论上单颗卫星无法完成所有任务")
    else:
        print("  ⚠️ 理论上单颗卫星可以完成所有任务 - 这可能是问题所在!")
    
    # 分析实际的资源消耗模式
    print(f"\n实际资源消耗模式分析:")
    
    # 检查内存消耗是否与任务持续时间相关
    memory_duration_correlation = np.corrcoef(memory_needs, task_durations)[0, 1]
    power_duration_correlation = np.corrcoef(power_needs, task_durations)[0, 1]
    
    print(f"  内存需求与持续时间相关性: {memory_duration_correlation:.3f}")
    print(f"  能量需求与持续时间相关性: {power_duration_correlation:.3f}")
    
    return memory_ratio, power_ratio

def test_resource_update_logic():
    """测试资源更新逻辑是否正确"""
    print("\n🔍 测试资源更新逻辑...")
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=10,
        num_nodes=args.num_nodes,
        num_satellites=args.num_satellites,
        memory_total=args.memory_total,
        power_total=args.power_total
    )
    
    static, dynamic, _ = test_data[0]
    static = static.unsqueeze(0)  # 添加batch维度
    dynamic = dynamic.unsqueeze(0)
    
    print(f"初始资源状态:")
    for sat_idx in range(args.num_satellites):
        initial_memory = dynamic[0, 2, 0, sat_idx].item()
        initial_power = dynamic[0, 3, 0, sat_idx].item()
        print(f"  卫星{sat_idx}: 内存={initial_memory:.4f}, 能量={initial_power:.4f}")
    
    # 模拟执行几个任务
    chosen_tasks = [1, 2, 3, 4, 5]  # 选择前5个任务
    chosen_satellite = 0  # 都分配给卫星0
    
    current_dynamic = dynamic.clone()
    
    for i, task_idx in enumerate(chosen_tasks):
        print(f"\n执行任务 {task_idx}:")
        
        # 获取任务资源需求
        memory_need = static[0, 5, task_idx].item()
        power_need = static[0, 6, task_idx].item()
        duration = static[0, 3, task_idx].item()
        
        print(f"  任务需求: 内存={memory_need:.4f}, 能量={power_need:.4f}, 持续时间={duration:.4f}")
        
        # 检查当前资源状态
        current_memory = current_dynamic[0, 2, 0, chosen_satellite].item()
        current_power = current_dynamic[0, 3, 0, chosen_satellite].item()
        
        print(f"  执行前资源: 内存={current_memory:.4f}, 能量={current_power:.4f}")
        
        # 检查是否满足资源约束
        memory_sufficient = current_memory >= memory_need
        power_sufficient = current_power >= power_need
        
        print(f"  资源检查: 内存{'✅' if memory_sufficient else '❌'}, 能量{'✅' if power_sufficient else '❌'}")
        
        if not (memory_sufficient and power_sufficient):
            print(f"  ⚠️ 资源不足，但系统可能仍然允许执行!")
        
        # 模拟资源更新（简化版本）
        new_memory = max(0, current_memory - memory_need)
        new_power = max(0, current_power - power_need)
        
        # 更新动态状态
        current_dynamic[0, 2, :, chosen_satellite] = new_memory
        current_dynamic[0, 3, :, chosen_satellite] = new_power
        
        print(f"  执行后资源: 内存={new_memory:.4f}, 能量={new_power:.4f}")

def test_mask_generation():
    """测试掩码生成是否正确排除资源不足的任务"""
    print("\n🔍 测试掩码生成逻辑...")
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=20,
        num_nodes=args.num_nodes,
        num_satellites=args.num_satellites,
        memory_total=args.memory_total,
        power_total=args.power_total
    )
    
    static, dynamic, _ = test_data[0]
    static = static.unsqueeze(0)
    dynamic = dynamic.unsqueeze(0)
    
    # 人为降低某颗卫星的资源
    low_resource_sat = 0
    dynamic[0, 2, :, low_resource_sat] = 0.05  # 很少的内存
    dynamic[0, 3, :, low_resource_sat] = 0.5   # 很少的能量
    
    print(f"人为设置卫星{low_resource_sat}的资源很低:")
    print(f"  内存: {dynamic[0, 2, 0, low_resource_sat].item():.4f}")
    print(f"  能量: {dynamic[0, 3, 0, low_resource_sat].item():.4f}")
    
    # 生成掩码
    mask, satellite_masks = test_data.update_mask(dynamic, static=static)
    
    print(f"\n掩码生成结果:")
    print(f"  总体掩码中可执行任务数: {mask.sum().item()}")
    
    for sat_idx in range(args.num_satellites):
        sat_mask = satellite_masks[0, :, sat_idx]
        executable_tasks = sat_mask.sum().item()
        print(f"  卫星{sat_idx}可执行任务数: {executable_tasks}")
        
        if sat_idx == low_resource_sat and executable_tasks > 0:
            print(f"    ⚠️ 资源不足的卫星仍然可以执行{executable_tasks}个任务!")
            
            # 检查哪些任务仍然可执行
            executable_indices = torch.nonzero(sat_mask).squeeze().tolist()
            if not isinstance(executable_indices, list):
                executable_indices = [executable_indices]
            
            print(f"    可执行的任务索引: {executable_indices[:5]}...")  # 只显示前5个
            
            # 检查这些任务的资源需求
            for task_idx in executable_indices[:3]:  # 检查前3个
                memory_need = static[0, 5, task_idx].item()
                power_need = static[0, 6, task_idx].item()
                print(f"      任务{task_idx}: 需要内存={memory_need:.4f}, 能量={power_need:.4f}")

def main():
    """主诊断函数"""
    print("🚀 开始诊断资源约束问题")
    print("=" * 60)
    
    # 分析资源约束的理论限制
    memory_ratio, power_ratio = analyze_resource_constraints()
    
    # 测试资源更新逻辑
    test_resource_update_logic()
    
    # 测试掩码生成
    test_mask_generation()
    
    print("\n" + "=" * 60)
    print("🎯 诊断结论:")
    
    if memory_ratio <= 1 and power_ratio <= 1:
        print("❌ 关键问题: 资源配置过于宽松!")
        print("   单颗卫星的资源足以完成大部分甚至全部任务")
        print("   这解释了为什么会出现严重的负载不均衡")
        print("\n💡 建议解决方案:")
        print("   1. 降低卫星资源总量 (memory_total, power_total)")
        print("   2. 增加任务的资源需求")
        print("   3. 增加任务数量")
        print("   4. 引入更严格的资源约束机制")
    else:
        print("✅ 资源配置合理，问题可能在于:")
        print("   1. 资源约束检查逻辑有漏洞")
        print("   2. 掩码生成不正确")
        print("   3. 资源更新逻辑有问题")

if __name__ == "__main__":
    main()
