import argparse

parser = argparse.ArgumentParser(description='Combinatorial Optimization')
# model
# pn
# gpn
# gpn_transformer
parser.add_argument('--model', default='gpn_transformer')
# train
parser.add_argument('--task', default='constellation_smp')
parser.add_argument('--seed', default=12346, type=int)  # 12345
parser.add_argument('--checkpoint', default=None)
parser.add_argument('--test', action='store_true', default=False)
# 梯度归一化和正则化参数
parser.add_argument('--max_grad_norm', default=1.0, type=float, help='梯度裁剪阈值')  # 提升梯度裁剪阈值
parser.add_argument('--advantage_threshold', default=50.0, type=float, help='动态梯度归一化阈值 (100节点任务)')
parser.add_argument('--dropout', default=0.15, type=float, help='增强正则化的dropout率')
# 学习率 - 提升学习率以加快收敛
parser.add_argument('--actor_lr', default=1e-4, type=float)  # 提升2倍
parser.add_argument('--critic_lr', default=5e-4, type=float)  # 提升10倍，critic需要更快学习
parser.add_argument('--weight_decay', type=float, default=5e-4, help='增强的权重衰减 (L2 regularization)')
parser.add_argument('--train_size', default=100000, type=int)
parser.add_argument('--valid_size', default=10000, type=int)
parser.add_argument('--epochs', default=3, type=int)
parser.add_argument('--lr', type=float, default=2e-4, help="learning rate")
parser.add_argument('--nodes', dest='num_nodes', default=100, type=int)
parser.add_argument('--hidden', dest='hidden_size', default=256, type=int)
parser.add_argument('--batch_size', default=32, type=int)
parser.add_argument('--static_size', default=9, type=int)
parser.add_argument('--dynamic_size', default=7, type=int)

parser.add_argument('--memory_total', default=0.3, type=float)
parser.add_argument('--power_total', default=5, type=float)

# 星座相关参数
parser.add_argument('--num_satellites', default=3, type=int, help='卫星星座中的卫星数量')
parser.add_argument('--constellation_mode', default='competitive', type=str,
                    help='星座工作模式: cooperative(协同), competitive(竞争), hybrid(混合)')
parser.add_argument('--task_sharing', action='store_true', default=True,
                    help='是否允许卫星间共享任务信息')

# 训练模式选择参数
parser.add_argument('--training_mode', default='all', type=str,
                    choices=['single', 'all', 'compare'],
                    help='训练模式选择: single(单一模式), all(所有模式), compare(对比模式)')
parser.add_argument('--modes_to_train', default=None, type=str, nargs='+',
                    choices=['cooperative', 'competitive', 'hybrid'],
                    help='指定要训练的星座模式列表，如: --modes_to_train cooperative competitive')
parser.add_argument('--communication_delay', default=0.01, type=float, 
                    help='星座内卫星间通信延迟')
parser.add_argument('--satellite_distance', default=0.5, type=float,
                    help='卫星间平均距离（归一化）')
parser.add_argument('--verbose', action='store_true', default=True,
                    help='是否打印详细训练信息')

# MultiHead_Additive_Attention
parser.add_argument('--attention', default='MultiHead_Additive_Attention', type=str)
parser.add_argument('--n_head', default=8, type=int)

# Transformer相关参数
parser.add_argument('--d_model', default=128, type=int, help='Transformer模型维度')
parser.add_argument('--n_transformer_layers', default=4, type=int, help='Transformer编码器层数')
parser.add_argument('--d_ff', default=2048, type=int, help='前馈网络维度')
parser.add_argument('--transformer_dropout', default=0.1, type=float, help='Transformer dropout率')

# lstm
# indrnn
# indrnnv2
parser.add_argument('--rnn', default='indrnn', type=str)
parser.add_argument('--layers', dest='num_layers', default=2, type=int)

# conv1d
parser.add_argument('--encoder', default='conv1d', type=str)

args = parser.parse_args()
