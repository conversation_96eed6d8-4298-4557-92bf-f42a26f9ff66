"""
测试负载均衡修复效果的脚本
"""
import torch
import numpy as np
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward
from constellation_smp.model_factory import ModelManager
from hyperparameter import args
import matplotlib.pyplot as plt

def test_satellite_access_distribution():
    """测试卫星访问权限分布是否均衡"""
    print("🔍 测试卫星访问权限分布...")
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=100,
        num_nodes=args.num_nodes,
        num_satellites=args.num_satellites,
        memory_total=args.memory_total,
        power_total=args.power_total
    )
    
    # 统计每颗卫星的访问权限
    satellite_access_counts = [0] * args.num_satellites
    total_tasks = 0
    
    for i in range(10):  # 检查前10个样本
        static, dynamic, _ = test_data[i]
        
        # 从dynamic中提取卫星访问信息
        # dynamic[:, 1, :, sat_idx] 表示第sat_idx颗卫星对各任务的访问权限
        for task_idx in range(args.num_nodes):
            total_tasks += 1
            for sat_idx in range(args.num_satellites):
                if dynamic[1, task_idx, sat_idx] > 0:  # 该卫星可以访问该任务
                    satellite_access_counts[sat_idx] += 1
    
    print("卫星访问权限统计:")
    for sat_idx in range(args.num_satellites):
        percentage = (satellite_access_counts[sat_idx] / total_tasks) * 100
        print(f"  卫星{sat_idx}: {satellite_access_counts[sat_idx]}/{total_tasks} ({percentage:.1f}%)")
    
    # 计算分布均衡性
    access_array = np.array(satellite_access_counts)
    std_dev = np.std(access_array)
    mean_access = np.mean(access_array)
    cv = std_dev / (mean_access + 1e-8)
    
    print(f"访问权限分布均衡性:")
    print(f"  标准差: {std_dev:.2f}")
    print(f"  变异系数: {cv:.3f} ({'均衡' if cv < 0.2 else '不均衡'})")

def test_load_balance_mechanism():
    """测试负载均衡机制"""
    print("\n🔍 测试负载均衡机制...")
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=10,
        num_nodes=args.num_nodes,
        num_satellites=args.num_satellites,
        memory_total=args.memory_total,
        power_total=args.power_total
    )
    
    # 创建模型
    model_manager = ModelManager(args.model, args, test_data)
    actor = model_manager.actor
    
    # 测试数据
    static, dynamic, _ = test_data[0]
    static = static.unsqueeze(0).repeat(4, 1, 1)  # batch_size=4
    dynamic = dynamic.unsqueeze(0).repeat(4, 1, 1, 1)
    
    # 前向传播
    with torch.no_grad():
        tour_indices, satellite_indices, _ = actor(static, dynamic)
    
    # 统计任务分配
    batch_size = static.size(0)
    for b in range(batch_size):
        satellite_task_counts = [0] * args.num_satellites
        total_tasks = 0
        
        for i in range(len(satellite_indices[b])):
            if i > 0:  # 跳过起始点
                sat_idx = satellite_indices[b][i].item()
                if 0 <= sat_idx < args.num_satellites:
                    satellite_task_counts[sat_idx] += 1
                    total_tasks += 1
        
        print(f"批次{b} 任务分配:")
        for sat_idx in range(args.num_satellites):
            percentage = (satellite_task_counts[sat_idx] / max(total_tasks, 1)) * 100
            print(f"  卫星{sat_idx}: {satellite_task_counts[sat_idx]}个任务 ({percentage:.1f}%)")
        
        # 计算负载均衡分数
        if total_tasks > 0:
            task_counts_array = np.array(satellite_task_counts)
            std_dev = np.std(task_counts_array)
            mean_tasks = np.mean(task_counts_array)
            cv = std_dev / (mean_tasks + 1e-8)
            idle_count = np.sum(task_counts_array == 0)
            
            balance_score = -cv * 2.0 - idle_count * 1.0
            print(f"  负载均衡分数: {balance_score:.3f}")
            print(f"  变异系数: {cv:.3f}")
            print(f"  空闲卫星数: {idle_count}")

def test_reward_components():
    """测试奖励函数各组件的贡献"""
    print("\n🔍 测试奖励函数组件...")
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=10,
        num_nodes=args.num_nodes,
        num_satellites=args.num_satellites,
        memory_total=args.memory_total,
        power_total=args.power_total
    )
    
    static, dynamic, _ = test_data[0]
    static = static.unsqueeze(0)
    dynamic = dynamic.unsqueeze(0)
    
    # 创建测试的任务分配
    tour_length = 20
    tour_indices = torch.randint(1, args.num_nodes, (1, tour_length))
    
    # 测试不同的负载分配情况
    scenarios = [
        ("均衡分配", [7, 7, 6]),
        ("轻微不均衡", [8, 6, 6]),
        ("严重不均衡", [15, 3, 2]),
        ("极端不均衡", [18, 1, 1])
    ]
    
    for name, task_distribution in scenarios:
        # 构造对应的satellite_indices
        satellite_indices = []
        for sat_idx, count in enumerate(task_distribution):
            satellite_indices.extend([sat_idx] * count)
        
        # 补齐到tour_length
        while len(satellite_indices) < tour_length:
            satellite_indices.append(0)
        satellite_indices = satellite_indices[:tour_length]
        satellite_indices = torch.tensor([satellite_indices])
        
        # 计算奖励
        reward_val, revenue_rate, distance, memory, power = reward(
            static, tour_indices, satellite_indices, 'cooperative'
        )
        
        print(f"\n{name} - 任务分配{task_distribution}:")
        print(f"  总奖励: {reward_val.item():.4f}")
        print(f"  收益率: {revenue_rate.item():.4f}")
        
        # 分析奖励组成
        from constellation_smp.constellation_smp import (
            REWARD_PROPORTION, DISTANCE_PROPORTION, MEMORY_PROPORTION,
            POWER_PROPORTION, LOAD_BALANCE_WEIGHT
        )
        
        revenue_component = REWARD_PROPORTION * revenue_rate.item()
        distance_component = DISTANCE_PROPORTION * distance.item()
        memory_component = MEMORY_PROPORTION * memory.item()
        power_component = POWER_PROPORTION * power.item()
        
        # 估算负载均衡组件（简化计算）
        task_counts = np.array(task_distribution)
        std_dev = np.std(task_counts)
        mean_tasks = np.mean(task_counts)
        cv = std_dev / (mean_tasks + 1e-8)
        idle_count = np.sum(task_counts == 0)
        balance_score = -cv * 2.0 - idle_count * 1.0
        balance_component = LOAD_BALANCE_WEIGHT * balance_score
        
        print(f"  奖励组成:")
        print(f"    收益贡献: {revenue_component:.4f}")
        print(f"    距离惩罚: {distance_component:.4f}")
        print(f"    内存惩罚: {memory_component:.4f}")
        print(f"    能量惩罚: {power_component:.4f}")
        print(f"    负载均衡: {balance_component:.4f}")

def main():
    """主测试函数"""
    print("🚀 开始测试负载均衡修复效果")
    print("=" * 60)
    
    # 测试卫星访问权限分布
    test_satellite_access_distribution()
    
    # 测试负载均衡机制
    test_load_balance_mechanism()
    
    # 测试奖励函数组件
    test_reward_components()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    
    print("\n📋 修复效果分析:")
    print("1. 检查卫星访问权限是否更均衡")
    print("2. 验证负载均衡机制是否有效工作")
    print("3. 确认奖励函数中负载均衡的权重是否合适")
    print("4. 观察任务分配是否更加均衡")

if __name__ == "__main__":
    main()
