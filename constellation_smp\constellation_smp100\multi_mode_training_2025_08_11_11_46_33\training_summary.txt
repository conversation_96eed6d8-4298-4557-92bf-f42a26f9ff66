🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_11_11_46_33
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_11_11_52_29
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 7.1411, Loss: 47.2246, Revenue: 0.4350, LoadBalance: -1.0652, Tasks: [S0:746(44.0%), S1:241(14.2%), S2:709(41.8%)], ActorGrad: 62.8280, CriticGrad: 319.8544, Advantage: μ=1.561, σ=0.843, range=[-0.34, 3.81]
Epoch 1, Batch 50/3125, loss: 38.009↓, reward: 8.583↑, critic_reward: 3.583, revenue_rate: 0.5738, distance: 10.6334, memory: 0.0397, power: 0.2740, lr: 0.000050, took: 370.373s
Batch 50: Reward: 9.3494, Loss: 47.6224, Revenue: 0.6231, LoadBalance: -2.1994, Tasks: [S0:988(40.6%), S1:24(1.0%), S2:1420(58.4%)], ActorGrad: 62.2075, CriticGrad: 74.6759, Advantage: μ=1.285, σ=1.234, range=[-1.46, 3.36]
Epoch 1, Batch 100/3125, loss: 43.088↓, reward: 9.980↑, critic_reward: 4.304, revenue_rate: 0.6202, distance: 11.0871, memory: 0.0262, power: 0.2945, lr: 0.000050, took: 363.871s
Batch 100: Reward: 10.6335, Loss: 46.5824, Revenue: 0.6281, LoadBalance: -2.3614, Tasks: [S0:1025(45.8%), S1:16(0.7%), S2:1199(53.5%)], ActorGrad: 50.4211, CriticGrad: 69.4993, Advantage: μ=1.585, σ=0.795, range=[-0.41, 3.13]
Epoch 1, Batch 150/3125, loss: 44.644↓, reward: 10.342↑, critic_reward: 4.510, revenue_rate: 0.6281, distance: 11.1101, memory: 0.0204, power: 0.2985, lr: 0.000050, took: 366.240s
Batch 150: Reward: 9.6557, Loss: 30.6793, Revenue: 0.6028, LoadBalance: -2.2242, Tasks: [S0:924(42.5%), S1:24(1.1%), S2:1228(56.4%)], ActorGrad: 43.6536, CriticGrad: 63.8851, Advantage: μ=1.590, σ=0.784, range=[-0.28, 2.81]
Epoch 1, Batch 200/3125, loss: 41.562↑, reward: 10.155↑, critic_reward: 4.652, revenue_rate: 0.6292, distance: 11.2191, memory: 0.0225, power: 0.2986, lr: 0.000050, took: 369.414s
Batch 200: Reward: 10.3278, Loss: 42.0223, Revenue: 0.6367, LoadBalance: -2.3361, Tasks: [S0:1063(46.1%), S1:15(0.7%), S2:1226(53.2%)], ActorGrad: 36.3907, CriticGrad: 71.5849, Advantage: μ=1.527, σ=0.905, range=[-0.48, 3.76]
Epoch 1, Batch 250/3125, loss: 41.693↓, reward: 10.438↓, critic_reward: 4.743, revenue_rate: 0.6375, distance: 11.2170, memory: 0.0214, power: 0.3009, lr: 0.000050, took: 367.567s
Batch 250: Reward: 9.8511, Loss: 37.2370, Revenue: 0.6309, LoadBalance: -2.5811, Tasks: [S0:1000(42.2%), S1:8(0.3%), S2:1360(57.4%)], ActorGrad: 49.1754, CriticGrad: 61.5379, Advantage: μ=1.485, σ=0.974, range=[-0.66, 3.28]
Epoch 1, Batch 300/3125, loss: 42.152↑, reward: 10.575↑, critic_reward: 4.835, revenue_rate: 0.6396, distance: 11.2335, memory: 0.0195, power: 0.3037, lr: 0.000050, took: 384.070s
Batch 300: Reward: 9.9194, Loss: 35.9985, Revenue: 0.6009, LoadBalance: -2.5009, Tasks: [S0:1020(46.9%), S1:8(0.4%), S2:1148(52.8%)], ActorGrad: 37.1667, CriticGrad: 55.7083, Advantage: μ=1.483, σ=0.977, range=[-0.76, 3.32]
Epoch 1, Batch 350/3125, loss: 41.881↑, reward: 10.766↑, critic_reward: 4.980, revenue_rate: 0.6428, distance: 11.2417, memory: 0.0161, power: 0.3047, lr: 0.000050, took: 370.626s
Batch 350: Reward: 10.7987, Loss: 38.2125, Revenue: 0.6554, LoadBalance: -2.6640, Tasks: [S0:1119(47.3%), S1:4(0.2%), S2:1245(52.6%)], ActorGrad: 38.2482, CriticGrad: 66.2027, Advantage: μ=1.621, σ=0.717, range=[0.03, 3.20]
Epoch 1, Batch 400/3125, loss: 39.693↓, reward: 10.568↓, critic_reward: 5.065, revenue_rate: 0.6454, distance: 11.2560, memory: 0.0178, power: 0.3056, lr: 0.000050, took: 364.109s
Batch 400: Reward: 10.4603, Loss: 35.3470, Revenue: 0.6388, LoadBalance: -2.8875, Tasks: [S0:701(31.3%), S1:6(0.3%), S2:1533(68.4%)], ActorGrad: 34.0338, CriticGrad: 60.6860, Advantage: μ=1.591, σ=0.782, range=[0.18, 3.18]
