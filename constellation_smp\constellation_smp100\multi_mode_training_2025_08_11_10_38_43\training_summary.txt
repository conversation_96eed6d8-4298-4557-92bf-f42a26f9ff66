🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_11_10_38_43
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_11_10_44_39
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 2.1200, Loss: 17.1662, Revenue: 0.4350, LoadBalance: -1.0652, Tasks: [S0:746(44.0%), S1:241(14.2%), S2:709(41.8%)], ActorGrad: 76.0141, CriticGrad: 54.7474, Advantage: μ=0.447, σ=1.738, range=[-4.81, 3.55]
Epoch 1, Batch 50/3125, loss: 74.052↑, reward: 9.676↑, critic_reward: 3.500, revenue_rate: 0.3233, distance: 5.6834, memory: 0.0381, power: 0.1534, lr: 0.000050, took: 203.367s
Batch 50: Reward: 10.6754, Loss: 72.0779, Revenue: 0.2995, LoadBalance: -0.5092, Tasks: [S0:348(32.0%), S1:367(33.7%), S2:373(34.3%)], ActorGrad: 36.7446, CriticGrad: 76.8807, Advantage: μ=1.349, σ=1.161, range=[-1.91, 3.34]
Epoch 1, Batch 100/3125, loss: 67.705↑, reward: 10.103↑, critic_reward: 4.280, revenue_rate: 0.3125, distance: 5.4487, memory: 0.0371, power: 0.1488, lr: 0.000050, took: 192.627s
Batch 100: Reward: 7.9040, Loss: 42.2348, Revenue: 0.2714, LoadBalance: -0.5920, Tasks: [S0:321(32.4%), S1:363(36.6%), S2:308(31.0%)], ActorGrad: 25.7638, CriticGrad: 48.6849, Advantage: μ=0.931, σ=1.527, range=[-2.77, 3.56]
Epoch 1, Batch 150/3125, loss: 62.804↑, reward: 9.752↑, critic_reward: 4.451, revenue_rate: 0.3076, distance: 5.2670, memory: 0.0321, power: 0.1450, lr: 0.000050, took: 187.297s
Batch 150: Reward: 9.0761, Loss: 55.9456, Revenue: 0.3109, LoadBalance: -0.5331, Tasks: [S0:441(36.3%), S1:369(30.3%), S2:406(33.4%)], ActorGrad: 34.7549, CriticGrad: 56.8190, Advantage: μ=1.085, σ=1.418, range=[-2.34, 3.20]
Epoch 1, Batch 200/3125, loss: 65.604↑, reward: 10.107↑, critic_reward: 4.584, revenue_rate: 0.3159, distance: 5.5484, memory: 0.0298, power: 0.1502, lr: 0.000050, took: 196.055s
Batch 200: Reward: 9.1418, Loss: 46.6806, Revenue: 0.2787, LoadBalance: -0.5259, Tasks: [S0:324(32.7%), S1:314(31.7%), S2:354(35.7%)], ActorGrad: 28.5654, CriticGrad: 70.5545, Advantage: μ=1.105, σ=1.402, range=[-1.60, 3.15]
Epoch 1, Batch 250/3125, loss: 65.904↓, reward: 10.339↓, critic_reward: 4.722, revenue_rate: 0.3163, distance: 5.4725, memory: 0.0383, power: 0.1495, lr: 0.000050, took: 194.440s
Batch 250: Reward: 9.7647, Loss: 65.8154, Revenue: 0.3348, LoadBalance: -0.5398, Tasks: [S0:402(31.4%), S1:501(39.1%), S2:377(29.5%)], ActorGrad: 25.0923, CriticGrad: 77.7998, Advantage: μ=1.076, σ=1.425, range=[-1.69, 3.38]
Epoch 1, Batch 300/3125, loss: 63.472↑, reward: 10.342↑, critic_reward: 4.877, revenue_rate: 0.3156, distance: 5.5073, memory: 0.0388, power: 0.1498, lr: 0.000050, took: 193.868s
Batch 300: Reward: 10.4295, Loss: 71.2199, Revenue: 0.3059, LoadBalance: -0.5213, Tasks: [S0:402(35.9%), S1:343(30.6%), S2:375(33.5%)], ActorGrad: 30.6282, CriticGrad: 73.2308, Advantage: μ=1.198, σ=1.321, range=[-1.31, 3.23]
