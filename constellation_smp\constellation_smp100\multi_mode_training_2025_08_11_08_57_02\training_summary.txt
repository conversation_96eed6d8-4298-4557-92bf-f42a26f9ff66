🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_11_08_57_02
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_11_08_57_05
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 6.6254, Loss: 38.9649, Revenue: 0.4493, LoadBalance: 0.4586, Tasks: [S0:798(43.8%), S1:251(13.8%), S2:775(42.5%)], ActorGrad: 72.0482, CriticGrad: 318.9863, Advantage: μ=1.513, σ=0.929, range=[-0.02, 3.64]

📊 Epoch 1 训练统计:
  平均奖励: 7.5830
  平均损失: 29.3281
  平均收益率: 0.5245
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 10.429, revenue_rate: 0.6109, efficiency: 0.4079, distance: 9.8291, memory: -0.0109, power: 0.2793
Test Summary - Avg reward: 9.647±2.914, revenue_rate: 0.6333±0.0497, efficiency: 0.4700, completion_rate: 0.7418, distance: 11.2166, memory: 0.0206, power: 0.2986
Load Balance - Avg balance score: -0.0245±0.0914
Task Distribution by Satellite:
  Satellite 1: 4992 tasks (68.05%)
  Satellite 2: 40 tasks (0.55%)
  Satellite 3: 2304 tasks (31.41%)
✅ 验证完成 - Epoch 1, reward: 9.647, revenue_rate: 0.6333, distance: 11.2166, memory: 0.0206, power: 0.2986
  ⚠️ 欠拟合: 训练验证差距 = -2.0636
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_11_08_57_05 (验证集奖励: 9.6467)

开始训练 Epoch 2/3
Batch 0: Reward: 8.6236, Loss: 33.7539, Revenue: 0.5988, LoadBalance: 0.0985, Tasks: [S0:1403(61.8%), S1:47(2.1%), S2:822(36.2%)], ActorGrad: 70.3806, CriticGrad: 63.6326, Advantage: μ=1.388, σ=1.112, range=[-1.12, 3.38]

📊 Epoch 2 训练统计:
  平均奖励: 8.8801
  平均损失: 33.4957
  平均收益率: 0.6332
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 10.912, revenue_rate: 0.7034, efficiency: 0.5275, distance: 11.4694, memory: 0.0423, power: 0.3284
Test Summary - Avg reward: 9.555±3.526, revenue_rate: 0.7107±0.0550, efficiency: 0.5997, completion_rate: 0.8435, distance: 12.8942, memory: 0.0522, power: 0.3396
Load Balance - Avg balance score: -0.5815±0.0613
Task Distribution by Satellite:
  Satellite 1: 7867 tasks (94.10%)
  Satellite 2: 30 tasks (0.36%)
  Satellite 3: 463 tasks (5.54%)
✅ 验证完成 - Epoch 2, reward: 9.555, revenue_rate: 0.7107, distance: 12.8942, memory: 0.0522, power: 0.3396
  ✅ 训练验证差距正常: -0.6752

开始训练 Epoch 3/3
Batch 0: Reward: 9.3526, Loss: 40.6569, Revenue: 0.6636, LoadBalance: 0.0000, Tasks: [S0:2214(88.7%), S1:21(0.8%), S2:261(10.5%)], ActorGrad: 41.6455, CriticGrad: 73.6766, Advantage: μ=1.417, σ=1.074, range=[-0.32, 3.73]

📊 Epoch 3 训练统计:
  平均奖励: 9.0986
  平均损失: 36.5150
  平均收益率: 0.7051
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 8.292, revenue_rate: 0.7636, efficiency: 0.7005, distance: 14.5346, memory: 0.1493, power: 0.3585
Test Summary - Avg reward: 10.800±3.330, revenue_rate: 0.8150±0.0594, efficiency: 0.7849, completion_rate: 0.9629, distance: 14.8864, memory: 0.1141, power: 0.3927
Load Balance - Avg balance score: -0.6906±0.0324
Task Distribution by Satellite:
  Satellite 1: 9395 tasks (98.40%)
  Satellite 2: 25 tasks (0.26%)
  Satellite 3: 128 tasks (1.34%)
✅ 验证完成 - Epoch 3, reward: 10.800, revenue_rate: 0.8150, distance: 14.8864, memory: 0.1141, power: 0.3927
  ✅ 训练验证差距正常: -1.7011
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_11_08_57_05 (验证集奖励: 10.7997)
训练完成

开始测试模型...
Test Batch 4/4, reward: 13.015, revenue_rate: 0.8388, efficiency: 0.7760, distance: 13.8613, memory: 0.1015, power: 0.4109
Test Summary - Avg reward: 10.634±3.369, revenue_rate: 0.8188±0.0586, efficiency: 0.7886, completion_rate: 0.9633, distance: 14.7502, memory: 0.1070, power: 0.4001
Load Balance - Avg balance score: -0.6902±0.0330
Task Distribution by Satellite:
  Satellite 1: 9397 tasks (98.38%)
  Satellite 2: 24 tasks (0.25%)
  Satellite 3: 131 tasks (1.37%)
测试完成 - 平均奖励: 10.634, 平均星座收益率: 0.8188
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_11_08_57_05
   平均奖励: 10.6338
   收益率: 0.8188

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_11_09_12_32
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 7.3833, Loss: 47.7797, Revenue: 0.4493, LoadBalance: 0.4586, Tasks: [S0:798(43.8%), S1:251(13.8%), S2:775(42.5%)], ActorGrad: 71.8554, CriticGrad: 361.5448, Advantage: μ=1.560, σ=0.844, range=[0.19, 3.51]

📊 Epoch 1 训练统计:
  平均奖励: 8.8210
  平均损失: 42.5571
  平均收益率: 0.5287
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 4/4, reward: 12.196, revenue_rate: 0.6181, efficiency: 0.4049, distance: 10.2825, memory: -0.0060, power: 0.2792
Test Summary - Avg reward: 11.485±2.973, revenue_rate: 0.6181±0.0550, efficiency: 0.4368, completion_rate: 0.7059, distance: 10.8404, memory: 0.0252, power: 0.2911
Load Balance - Avg balance score: 0.1035±0.0637
Task Distribution by Satellite:
  Satellite 1: 4043 tasks (57.92%)
  Satellite 2: 57 tasks (0.82%)
  Satellite 3: 2880 tasks (41.26%)
✅ 验证完成 - Epoch 1, reward: 11.485, revenue_rate: 0.6181, distance: 10.8404, memory: 0.0252, power: 0.2911
  ⚠️ 欠拟合: 训练验证差距 = -2.6635
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_11_09_12_32 (验证集奖励: 11.4846)

开始训练 Epoch 2/3
Batch 0: Reward: 9.5391, Loss: 45.4773, Revenue: 0.5892, LoadBalance: 0.1571, Tasks: [S0:1177(52.5%), S1:47(2.1%), S2:1016(45.4%)], ActorGrad: 62.8795, CriticGrad: 69.2682, Advantage: μ=1.467, σ=1.003, range=[-0.64, 2.91]
